<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}EEU Contractor System{% endblock %}</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
    />
    {% load static %}
    <style>
      :root {
        --primary-color: #2c3e50;
        --secondary-color: #4ca1af;
        --accent-color: #3498db;
        --sidebar-width: 260px;
        --sidebar-collapsed-width: 70px;
        --header-height: 60px;
        --transition-speed: 0.3s;
      }

      body {
        font-family: "Segoe UI", sans-serif;
        {% comment %} background-color: #f8f9fa; {% endcomment %}
        background-color:#FAFAFA;

        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }

      /* Top navbar styling */
      .top-navbar {
        background-color: var(--primary-color);
        color: white;
        padding: 0;
        height: var(--header-height);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1001;
      }

      .navbar-brand {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 600;
        font-size: 1.2rem;
        color: white;
      }

      .navbar-brand img {
        height: 40px;
        width: auto;
      }

      .navbar-brand:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      .navbar-nav .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        transition: all 0.2s;
      }

      .navbar-nav .nav-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
      }

      .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: var(--accent-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 10px;
      }

      /* Sidebar styling */
      .sidebar {
        position: fixed;
        top: var(--header-height);
        left: 0;
        height: calc(100vh - var(--header-height));
        width: var(--sidebar-width);
        background-color:#FAFAFA;

        background: linear-gradient(
          180deg,
          var(--primary-color) 0%,
          #1a252f 100%
          
        );
        color: white;
        transition: all var(--transition-speed);
        z-index: 1000;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
        overflow-x: hidden;
      }

      .sidebar.collapsed {
        width: var(--sidebar-collapsed-width);
      }

      .sidebar-toggle {
        background: transparent;
        border: none;
        color: white;
        cursor: pointer;
        font-size: 1.2rem;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        transition: background-color 0.2s;
        position: absolute;
        top: 15px;
        right: 15px;
      }

      .sidebar-toggle:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      .menu-category {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        color: rgba(255, 255, 255, 0.5);
        padding: 20px 20px 10px;
        margin: 0;
      }

      .collapsed .menu-category {
        text-align: center;
        padding: 20px 5px 10px;
      }

      .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.2s;
        cursor: pointer;
        position: relative;
      }

      .menu-item:hover,
      .menu-item.active {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
      }

      .menu-item i {
        font-size: 1.1rem;
        min-width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .menu-item span {
        margin-left: 10px;
        white-space: nowrap;
        transition: opacity var(--transition-speed);
      }

      .collapsed .menu-item span {
        opacity: 0;
        width: 0;
      }

      .menu-item .ms-auto {
        transition: transform 0.2s;
      }

      .menu-item.open .ms-auto {
        transform: rotate(180deg);
      }

      .submenu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
        background-color: rgba(0, 0, 0, 0.1);
      }

      .submenu.show {
        max-height: 500px;
      }

      .submenu-item {
        display: block;
        padding: 10px 20px 10px 60px;
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.2s;
      }

      .collapsed .submenu-item {
        padding-left: 20px;
      }

      .submenu-item:hover,
      .submenu-item.active {
        background-color: rgba(255, 255, 255, 0.05);
        color: white;
      }

      /* Main content styling */
      .main-content {
        margin-left: var(--sidebar-width);
        transition: margin var(--transition-speed);
        min-height: 100vh;
        padding: 20px;
        padding-top: calc(var(--header-height) + 20px);
      }

      .main-content.expanded {
        margin-left: var(--sidebar-collapsed-width);
      }

      /* Dropdown styling */
      .dropdown-menu {
        border: none;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        margin-top: 10px;
        padding: 10px;
      }

      .dropdown-item {
        padding: 8px 15px;
        border-radius: 4px;
        transition: all 0.2s;
      }

      .menu-item i,
.submenu-item i {
  color: orange;
}

    /* Make the submenu toggle arrow orange */
    .menu-item .ms-auto {
      color: orange;
    }
    .menu-item .ms-auto {
      transition: transform 0.2s;
      color: orange; /* <-- arrow icon color */
    }

    /* Add these new styles */
    .menu-item i,
    .submenu-item i {
      color: orange;
    }

    .submenu-item:hover i,
    .menu-item:hover i {
      color: #ff9933; /* Optional: brighter orange on hover */
    }


      .top-navbar i {
  color: orange;
  font-size: 1.2rem;
  margin-right: 8px;
}

/* Make logo circular with optional orange border */
.navbar-brand img {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid orange;
}  
.gradient-text2 {
  font-family: 'Times New Roman', Times, serif;
  font-size: 20px;
  {% comment %} background: linear-gradient(to bottom, orange 10%, green 90%); {% endcomment %}
  background-clip: text;
  color:orange;
  letter-spacing: 2px;         /* Adjust as needed */
  word-spacing: 3px;           /* Adjust as needed */
}


      .dropdown-item:hover {
        background-color: rgba(44, 62, 80, 0.1);
        transform: translateX(5px);
      }

      /* Responsive adjustments */
      @media (max-width: 992px) {
        .sidebar {
          transform: translateX(-100%);
        }

        .sidebar.mobile-show {
          transform: translateX(0);
        }

  
        .main-content {
          margin-left: 0;
        }
      }
    </style>
    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-expand-lg top-navbar">
      <div class="container-fluid">
       <a class="navbar-brand" href="{% url 'home' %}" style="display: flex; align-items: center;">
        <img src="{% static 'images/eeu_logo.png' %}" alt="EEU Logo" style="height: 40px; margin-right: 10px;" />
        <span class="gradient-text2">EEU Project Progress Management System</span>
      </a>




    <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            {% if user.is_staff %}
            <li class="nav-item">
              <a class="nav-link" href="/admin/">
                <i class="bi bi-gear"></i> Admin Panel
              </a>
            </li>
            {% endif %}

            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle d-flex align-items-center"
                href="#"
                id="userDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <div class="user-avatar">{{ user.username|first|upper }}</div>
                {{ user.get_full_name|default:user.username }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                
                <li>
                <a class="dropdown-item" href="#" id="showPasswordForm">
                  <i class="bi bi-gear me-2"></i> Settings
                </a>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{% url 'logout' %}"
                    ><i class="bi bi-box-arrow-right me-2"></i>Logout</a
                  >
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
      <button class="sidebar-toggle" id="sidebarToggle">
        <i class="bi bi-chevron-left"></i>
      </button>

      <!-- Main Navigation -->
      <div class="sidebar-menu">
      <div class="menu-item" onclick="window.location.href='{% url 'dashboard' %}'">
          <i class="bi bi-speedometer2"></i>
          <span>Dashboard</span>
      </div>
        <!-- Vendors Section -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-building"></i>
          <span>Vendors</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>
        <div class="submenu">
          <a href="{% url 'vendors_registration' %}" class="submenu-item"
            >Register New Vendor</a
          >
          <a href="{% url 'vendors_list' %}" class="submenu-item"
            >All Vendors</a
          >

          <a href="{% url 'vendor_create' %}" class="submenu-item"
            >Vendor Full Form</a
          >
          <a href="{% url 'vendor_information_list' %}" class="submenu-item"
            >Vendor Information</a
          >
        </div>

        <!-- Projects -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-clipboard-check"></i>
          <span>Projects</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>
        <div class="submenu">
          <a href="{% url 'project_create' %}" class="submenu-item"
            >Create Project</a
          >
          <a href="{% url 'project_list' %}" class="submenu-item"
            >All Projects</a
          >
        </div>

        <!-- Project Progress -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-graph-up"></i>
          <span>Project Progress</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>
        <div class="submenu">
          <a href="{% url 'project_progress_list' %}" class="submenu-item"
            >All Progress Reports</a
          >
          <a href="{% url 'create_project_progress' %}" class="submenu-item"
            >Add Progress Report</a
          >
        </div>
    
     <!-- SubActivity Workload - FULLY STABLE SECTION -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-clipboard-data"></i>
          <span>Workloads</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>    

        <div class="submenu">
          <a href="{% url 'project_progress_list' %}" class="submenu-item">
            Browse Projects
          </a>

          {% if request.session.current_progress_id %}
            <a href="{% url 'workload_list' progress_id=request.session.current_progress_id %}" class="submenu-item">
              View Project Workloads
            </a>
            <a href="{% url 'add_workload' progress_id=request.session.current_progress_id %}" class="submenu-item">
              Add New Workload
            </a>
          {% else %}
            <span class="submenu-item text-muted">Select Project First</span>
          {% endif %}
        </div>
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-bar-chart-steps"></i>
          <span>Progress Records</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>

        <div class="submenu">
          {% if request.resolver_match.kwargs.workload_id %}
            <a href="{% url 'workload_detail' workload_id=request.resolver_match.kwargs.workload_id %}" class="submenu-item">
              Update Progress
            </a>
          {% else %}
            <span class="submenu-item text-muted">Select Workload First</span>
          {% endif %}
        </div>
      <!-- Vendor History -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-clock-history"></i>
          <span>Vendor History</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>
        <div class="submenu">
          <a href="{% url 'vendor_history_list' %}" class="submenu-item"
            >All Vendor History</a
          >
          <a href="{% url 'add_vendor_history' %}" class="submenu-item"
            >Add Vendor History</a
          >

          {% if request.resolver_match.kwargs.vendor_id %}
          <a
            href="{% url 'vendor_history_list' vendor_id=request.resolver_match.kwargs.vendor_id %}"
            class="submenu-item"
            >Vendor Specific History</a
          >
          <a
            href="{% url 'add_vendor_history' vendor_id=request.resolver_match.kwargs.vendor_id %}"
            class="submenu-item"
            >Add History for Vendor</a
          >
          {% endif %} {% if request.resolver_match.kwargs.history_id %}
          <a
            href="{% url 'vendor_history_detail' history_id=request.resolver_match.kwargs.history_id %}"
            class="submenu-item"
            >History Details</a
          >
          <a
            href="{% url 'edit_vendor_history' history_id=request.resolver_match.kwargs.history_id %}"
            class="submenu-item"
            >Edit History</a
          >
          {% endif %}
        </div>
        <!-- Reports -->
        <div class="menu-item" onclick="toggleSubmenu(this)">
          <i class="bi bi-file-earmark-bar-graph"></i>
          <span>Reports</span>
          <i class="bi bi-chevron-down ms-auto"></i>
        </div>
        <div class="submenu">
          <a href="{% url 'vendor_performance_report' %}" class="submenu-item"
            >Vendor Performance</a
          >
          <a href="{% url 'project_status_report' %}" class="submenu-item"
            >Project Status</a
          >
          <a href="{% url 'progress_summary_report' %}" class="submenu-item"
            >Progress Summary</a
          >
        </div>

      <!-- Documents -->  
        <div class="menu-item" onclick="toggleSubmenu(this)">
              <i class="bi bi-file-earmark-text"></i>
              <span>Documents</span>
              <i class="bi bi-chevron-down ms-auto"></i>
            </div>
            <div class="submenu">
              <a href="{% url 'all_vendor_documents' %}" class="submenu-item">
                <i class="bi bi-collection"></i> All Documents
              </a>
              
              <!-- Add this line below -->
             <a href="{% url 'add_vendor_document' %}" class="submenu-item">
              <i class="bi bi-upload"></i> Upload Document
            </a>

            </div>



        <!-- Logout -->
        <a href="{% url 'logout' %}" class="menu-item">
          <i class="bi bi-box-arrow-right"></i>
          <span>Logout</span>
        </a>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
      {% block content %}
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="card shadow-sm mb-4">
              <div class="card-body">
                <h5 class="card-title">
                  Welcome to EEU Project Progress Management System
                </h5>
                <p class="card-text">
                  This system helps manage contractors, projects, and work
                  progress for the Ethiopian Electric Utility.
                </p>
                <p>
                  Use the sidebar navigation to access different sections of the
                  application.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endblock %}
    </div>
<!-- Floating Change Password Form -->
<div id="passwordFormContainer" class="top-right-form d-none" style="font-family: 'Times New Roman', Times, serif; color:'orange';">
  <div class="d-flex justify-between align-items-center mb-2">
    <h6 class="flex-grow-1 text-center w-100 m-0">Change Password</h6>
   <button type="button" id="closePasswordForm" aria-label="Close"  style=" position: absolute;top: 8px;right: 10px;
    width: 28px; height: 28px;border-radius: 50%; border: none; background-color: red; color: white; font-size: 18px;
    line-height: 1; text-align: center; padding: 0; cursor: pointer;  "> × </button>
  </div>
  <div id="passwordFormMessages"></div>
  <form id="passwordChangeForm" method="POST">
    {% csrf_token %}
    <label for="current_password">Current Password:</label>
    <input type="password" name="current_password" id="current_password" class="form-control mb-2" required>

    <label for="new_password">New Password:</label>
    <input type="password" name="new_password" id="new_password" class="form-control mb-2" required>

    <label for="confirm_password">Confirm New Password:</label>
    <input type="password" name="confirm_password" id="confirm_password" class="form-control mb-2" required>

    <button type="submit" class="btn btn-success w-100 mt-2">Change Password</button>
  </form>
</div>

<style>
.top-right-form {
  position: fixed;
  top: 70px;
  right: 20px;
  width: 250px;
  padding: 10px;
  background-color: #fff;
  border: 2px solid rgb(17, 17, 18);
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
  z-index: 2000;
}
</style>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Toggle sidebar
      document
        .getElementById("sidebarToggle")
        .addEventListener("click", function () {
          document.getElementById("sidebar").classList.toggle("collapsed");
          document.getElementById("mainContent").classList.toggle("expanded");
        });

      // Toggle submenu
      function toggleSubmenu(element) {
        const submenu = element.nextElementSibling;
        if (submenu && submenu.classList.contains("submenu")) {
          // Toggle the clicked submenu
          element.classList.toggle("open");
          submenu.classList.toggle("show");

          // If opening this submenu, close others
          if (submenu.classList.contains("show")) {
            const allSubmenus = document.querySelectorAll(".submenu.show");
            const allOpenItems = document.querySelectorAll(".menu-item.open");

            allSubmenus.forEach((menu) => {
              if (menu !== submenu) {
                menu.classList.remove("show");
              }
            });

            allOpenItems.forEach((item) => {
              if (item !== element) {
                item.classList.remove("open");
              }
            });
          }
        }
      }

      // Add active class to current page link and open parent submenu
      document.addEventListener("DOMContentLoaded", function () {
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll(
          ".menu-item, .submenu-item"
        );

        menuItems.forEach((item) => {
          const href = item.getAttribute("href");
          if (href && (currentPath === href || currentPath.startsWith(href))) {
            item.classList.add("active");

            // If it's a submenu item, open its parent menu
            if (item.classList.contains("submenu-item")) {
              const submenu = item.closest(".submenu");
              if (submenu) {
                submenu.classList.add("show");
                const parentMenuItem = submenu.previousElementSibling;
                if (parentMenuItem) {
                  parentMenuItem.classList.add("open");
                }
              }
            }
          }
        });
      });

      // Mobile menu toggle
      document
        .querySelector(".navbar-toggler")
        .addEventListener("click", function () {
          document.getElementById("sidebar").classList.toggle("mobile-show");
        });
    </script>
    
    {% block extra_js %}{% endblock %}
    <script>
document.addEventListener("DOMContentLoaded", function () {
  const toggleBtn = document.getElementById("showPasswordForm");
  const closeBtn = document.getElementById("closePasswordForm");
  const formBox = document.getElementById("passwordFormContainer");
  const passwordForm = document.getElementById("passwordChangeForm");
  const messageBox = document.getElementById("passwordFormMessages");

  // Toggle show/hide
  if (toggleBtn) {
    toggleBtn.addEventListener("click", function (e) {
      e.preventDefault();
      formBox.classList.toggle("d-none");
    });
  }

  if (closeBtn) {
    closeBtn.addEventListener("click", function () {
      formBox.classList.add("d-none");
    });
  }

  // Handle AJAX form submit
  passwordForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    const formData = new URLSearchParams(new FormData(passwordForm));

    fetch("{% url 'user_settings' %}", {
      method: "POST",
      headers: {
        "X-CSRFToken": csrfToken,
        "Content-Type": "application/x-www-form-urlencoded"
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      messageBox.innerHTML = `<div class="text-${data.status} mt-2">${data.message}</div>`;
      if (data.status === "success") {
        passwordForm.reset();
      }
    });
  });
});
</script>


  </body>
</html>

