from django.shortcuts import render, redirect, get_object_or_404
from django.views.decorators.http import condition
from django.utils.decorators import method_decorator
from django.core.cache import cache
from django.db.models import Count, Sum, Q
from django.core.paginator import Paginator
from django.http import Http404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView
from django.views.generic.edit import CreateView, UpdateView
from django.urls import reverse_lazy
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.exceptions import ValidationError
from django.http import JsonResponse
# from dateutil.relativedelta import relativedelta

# from dateutil.relativedelta import relativedelta
from .forms import VendorsRegistrationForm,VendorForm,SubActivityProgressForm,ProjectForm,SubActivityWorkloadForm,ProjectVendorsHistoryForm,VendorDocumentForm
from .models import Vendorsregistration,Vendor,Region,SubActivityWorkload,SubActivityProgress,ProjectVendorsHistory,VendorDocument,SubActivity,Project,ProjectProgress
from django.contrib.auth import logout,login
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required

from django.contrib import messages
from .models import Vendor, VendorDocument
from .forms import VendorDocumentForm
from django.forms import inlineformset_factory
from .forms import ProjectProgressForm, SubActivityWorkloadForm, SubActivityProgressFormSet
@login_required
def home(request):
    return render(request, 'base.html')
def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('home')  # after successful login go to home view
    else:
        form = AuthenticationForm()
    return render(request, 'login.html', {'form': form})
def logout_view(request):
    logout(request)
    return redirect('login') 
# Vendor registration views
@login_required
def vendors_registration_view(request):
    """View for registering new vendors (contractors or enterprises)."""
    if request.method == 'POST':
        form = VendorsRegistrationForm(request.POST)
        if form.is_valid():
            new_vendor = form.save()
            messages.success(request, f"{new_vendor.contractor_name} has been registered with identifier number {new_vendor.identification_number}")
            return redirect('vendor_detail', vendor_id=new_vendor.id)
        else:
            # Add debugging for form errors
            print(f"Form errors: {form.errors}")
    else:
        form = VendorsRegistrationForm()
    
    return render(request, 'vendors_Registration_Form.html', {
        'form': form
    })
@login_required
def vendors_list_view(request):
    contractor_type = request.GET.get('contractor_type', 'all')
    is_registered = request.GET.get('is_registered', 'All')
    search_query = request.GET.get('search', '')
   
    vendors = Vendorsregistration.objects.all()
    # Filtering logic
    if contractor_type != 'all':
        vendors = vendors.filter(contractor_type=contractor_type)
    if is_registered == 'Yes':
        vendors = vendors.filter(is_registered=True)
    elif is_registered == 'No':
        vendors = vendors.filter(is_registered=False)
    if search_query:
        vendors = vendors.filter(
            Q(contractor_name__icontains=search_query) |
            Q(identification_number__icontains=search_query) |
            Q(registrar_name__icontains=search_query)
        )

    # Stats
    total_count = vendors.count()
    contractor_count = vendors.filter(contractor_type='contractor').count()
    enterprise_count = vendors.filter(contractor_type='enterprise').count()
    new_enterprise_count = vendors.filter(contractor_type='new_enterprise').count()
    registered_count = vendors.filter(is_registered=True).count()
    unregistered_count = vendors.filter(is_registered=False).count()
    # Pagination
    paginator = Paginator(vendors.order_by('-registration_date'), 10)
    page = request.GET.get('page')
    page_obj = paginator.get_page(page)
    regions = Region.objects.all()
    context = {
        'vendors': page_obj,
        'page_obj': page_obj,
        'is_paginated': paginator.count > paginator.per_page,
        'contractor_type': contractor_type,
        'is_registered': is_registered,
        'search_query': search_query,
        'regions': regions,
        'total_count': total_count,
        'contractor_count': contractor_count,
        'enterprise_count': enterprise_count,
        'new_enterprise_count': new_enterprise_count,
        'registered_count': registered_count,
        'unregistered_count': unregistered_count,
    }

    return render(request, 'vendorsregistration_list.html', context)

@login_required
def registered_contractors_list(request):
    """View to display a list of registered contractors."""
    # Get the type filter parameter from the URL
    contractor_type = request.GET.get('type', 'all')
    # Base queryset - only show registered vendors
    queryset = Vendorsregistration.objects.filter(is_registered=True)
    # Apply filter based on type parameter
    if contractor_type == 'contractors':
        queryset = queryset.filter(contractor_type='contractor')
    elif contractor_type == 'enterprises':
        queryset = queryset.filter(contractor_type__in=['enterprise', 'new_enterprise'])
    # Order by registration_date (newest first) instead of created_at
    contractors = queryset.order_by('-registration_date')
    context = {
        'contractors': contractors,
        'total_count': contractors.count(),
        'contractor_type': contractor_type,
    }
    return render(request, 'registered_contractors_list.html', context)
@login_required
def new_enterprises_list(request):
    """View to display a list of all vendors with filtering options."""
    # Get the contractor_type filter parameter from the URL
    contractor_type = request.GET.get('contractor_type', None)
   # Base queryset
    queryset = Vendorsregistration.objects.all()
    # Apply filter based on contractor_type parameter
    if contractor_type in ['contractor', 'enterprise', 'new_enterprise']:
        queryset = queryset.filter(contractor_type=contractor_type)
        filter_applied = True
    else:
        # If no valid filter or 'all' is selected, show all records
        filter_applied = False
    
    # Order by registration_date (newest first) instead of created_at
    vendors = queryset.order_by('-registration_date')
    
    context = {
        'new_enterprises': vendors,  # Keep the same template variable name for compatibility
        'total_count': vendors.count(),
        'contractor_type': contractor_type if filter_applied else 'all',
        'filter_applied': filter_applied
    }
    return render(request, 'new_enterprises_list.html', context)

# Vendor detail view
@login_required
def vendor_detail_view(request, vendor_id):
    """View to display detailed information about a specific vendor."""
    try:
        vendor = Vendorsregistration.objects.get(id=vendor_id)
        
        # Calculate total engineers and staff
        total_engineers = (vendor.electrical_engineers or 0) + (vendor.electromechanical_engineers or 0) + (vendor.civil_engineers or 0)
        total_technicians = (vendor.electricians_foremen or 0) + (vendor.surveyors or 0)
        total_staff = total_engineers + total_technicians + (vendor.line_workers or 0)
        
        # 1. Human Resource Information criteria
        hr_info_criteria = [
            vendor.has_professional_license,  # Professional qualification
            vendor.has_human_resource         # Human resource sufficiency
        ]
        hr_info_satisfied = sum(1 for criterion in hr_info_criteria if criterion)
        hr_info_total = len(hr_info_criteria)
        hr_info_percentage = int((hr_info_satisfied / hr_info_total) * 100) if hr_info_total > 0 else 0
        hr_info_complete = hr_info_percentage == 100
        
        # 2. Professional Count criteria
        professional_count_criteria = [
            vendor.electrical_engineers > 0,
            vendor.electromechanical_engineers > 0,
            vendor.civil_engineers > 0,
            vendor.electricians_foremen > 0,
            vendor.surveyors > 0,
            vendor.line_workers > 0
        ]
        professional_count_satisfied = sum(1 for criterion in professional_count_criteria if criterion)
        professional_count_total = len(professional_count_criteria)
        professional_count_percentage = int((professional_count_satisfied / professional_count_total) * 100) if professional_count_total > 0 else 0
        professional_count_complete = professional_count_percentage == 100
        
        # 3. Document Checklist criteria
        document_checklist_criteria = [
            vendor.has_work_experience_doc,
            vendor.has_trade_license,
            vendor.has_energy_certification,
            vendor.has_equipment_resources,
            vendor.has_vat_and_tax,
            vendor.has_good_performance,
            vendor.has_financial_capacity
        ]
        document_checklist_satisfied = sum(1 for criterion in document_checklist_criteria if criterion)
        document_checklist_total = len(document_checklist_criteria)
        document_checklist_percentage = int((document_checklist_satisfied / document_checklist_total) * 100) if document_checklist_total > 0 else 0
        document_checklist_complete = document_checklist_percentage == 100
        
        # Calculate overall completion
        total_criteria_satisfied = hr_info_satisfied + professional_count_satisfied + document_checklist_satisfied
        total_criteria = hr_info_total + professional_count_total + document_checklist_total
        overall_completion_percentage = int((total_criteria_satisfied / total_criteria) * 100) if total_criteria > 0 else 0
        
        # Determine if all required sections are complete
        all_sections_complete = hr_info_complete and professional_count_complete and document_checklist_complete
        
        context = {
            'vendor': vendor,
            'total_engineers': total_engineers,
            'total_technicians': total_technicians,
            'total_staff': total_staff,
            
            # Human Resource Information
            'hr_info_satisfied': hr_info_satisfied,
            'hr_info_total': hr_info_total,
            'hr_info_percentage': hr_info_percentage,
            'hr_info_complete': hr_info_complete,
            
            # Professional Count
            'professional_count_satisfied': professional_count_satisfied,
            'professional_count_total': professional_count_total,
            'professional_count_percentage': professional_count_percentage,
            'professional_count_complete': professional_count_complete,
            
            # Document Checklist
            'document_checklist_satisfied': document_checklist_satisfied,
            'document_checklist_total': document_checklist_total,
            'document_checklist_percentage': document_checklist_percentage,
            'document_checklist_complete': document_checklist_complete,
            'document_completion': document_checklist_percentage,  # For backward compatibility
            
            # Overall Completion
            'total_criteria_satisfied': total_criteria_satisfied,
            'total_criteria': total_criteria,
            'overall_completion_percentage': overall_completion_percentage,
            'all_sections_complete': all_sections_complete
        }
        
        return render(request, 'vendor_detail.html', context)
        
    except Vendorsregistration.DoesNotExist:
        raise Http404(f"No vendor found with ID {vendor_id}")
# Class-based views
@method_decorator(login_required, name='dispatch')
class VendorsRegistrationListView(ListView):
    model = Vendorsregistration
    template_name = 'vendorsregistration_list.html'
    context_object_name = 'vendors'
    paginate_by = 10
    
    def get_queryset(self):
        queryset = Vendorsregistration.objects.all()
        
        # Filter by contractor type
        contractor_type = self.request.GET.get('contractor_type')
        if contractor_type and contractor_type != 'all':
            queryset = queryset.filter(contractor_type=contractor_type)
        
        # Filter by registration status
        is_registered = self.request.GET.get('is_registered')
        if is_registered == 'Yes':
            queryset = queryset.filter(is_registered=True)
        elif is_registered == 'No':
            queryset = queryset.filter(is_registered=False)
        
        # Filter by date range
        date_filter = self.request.GET.get('date_filter')
        if date_filter == 'today':
            today = timezone.now().date()
            queryset = queryset.filter(registration_date__date=today)
        elif date_filter == 'this_week':
            start_of_week = timezone.now().date() - timedelta(days=timezone.now().weekday())
            queryset = queryset.filter(registration_date__date__gte=start_of_week)
        elif date_filter == 'this_month':
            today = timezone.now().date()
            start_of_month = today.replace(day=1)
            queryset = queryset.filter(registration_date__date__gte=start_of_month)
        
        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(contractor_name__icontains=search_query) |
                Q(identification_number__icontains=search_query) |
                Q(registrar_name__icontains=search_query)
            )
        
        return queryset.order_by('-registration_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter parameters to context
        context['contractor_type'] = self.request.GET.get('contractor_type', 'all')
        context['is_registered'] = self.request.GET.get('is_registered', 'All')
        context['date_filter'] = self.request.GET.get('date_filter', 'any')
        context['search_query'] = self.request.GET.get('search', '')
        
        # Add counts for each contractor type
        context['total_count'] = Vendorsregistration.objects.count()
        context['contractor_count'] = Vendorsregistration.objects.filter(contractor_type='contractor').count()
        context['enterprise_count'] = Vendorsregistration.objects.filter(contractor_type='enterprise').count()
        context['new_enterprise_count'] = Vendorsregistration.objects.filter(contractor_type='new_enterprise').count()
        
        # Add counts for registration status
        context['registered_count'] = Vendorsregistration.objects.filter(is_registered=True).count()
        context['unregistered_count'] = Vendorsregistration.objects.filter(is_registered=False).count()
        
        return context


def vendor_create_view(request):
    if request.method == 'POST':
        form = VendorForm(request.POST)
        if form.is_valid():
            try:
                instance = form.save()
                messages.success(request, 'Vendor registered successfully!')
                return redirect('vendor_information_list')
            except ValidationError as ve:
                for field, error_list in ve.message_dict.items():
                    for error in error_list:
                        form.add_error(field if field != '__all__' else None, error)
            except Exception as e:
                form.add_error(None, f"Unexpected error: {str(e)}")
    else:
        form = VendorForm()
    return render(request, 'vendor_second_form.html', {'form': form})

@login_required
def vendor_list_view(request):
    vendors = Vendor.objects.select_related('vendor_registration').all()
    return render(request, 'vendor_information_list.html', {'vendors': vendors})


from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth import update_session_auth_hash

@login_required
def user_settings(request):
    user = request.user

    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        if not user.check_password(current_password):
            return JsonResponse({'status': 'danger', 'message': 'Current password is incorrect.'})
        elif new_password != confirm_password:
            return JsonResponse({'status': 'danger', 'message': 'New passwords do not match.'})
        else:
            user.set_password(new_password)
            user.save()
            update_session_auth_hash(request, user)
            return JsonResponse({'status': 'success', 'message': 'Password changed successfully.'})

    return JsonResponse({'status': 'danger', 'message': 'Invalid request'})

   
# API Endpoint
from django.http import JsonResponse

def project_vendors_api(request, project_id):
    project = get_object_or_404(Project, id=project_id)
    
    # Get vendors associated with this project
    vendors = Vendor.objects.filter(
        projectvendorshistory__project=project
    ).distinct()
    
    # Format data for JSON response
    vendors_data = [
        {
            'id': vendor.id,
            'name': vendor.name,
        }
        for vendor in vendors
    ]
    
    return JsonResponse({'vendors': vendors_data})



from django.shortcuts import redirect
from .forms import SubActivityProgressFormSet

@login_required
def select_project_progress(request, progress_id):
    request.session['current_progress_id'] = progress_id
    return redirect('workload_list', progress_id=progress_id)


# SubActivityWorkload Views
class SubActivityWorkloadListView(LoginRequiredMixin, ListView):
    model = SubActivityWorkload
    template_name = 'workload/workload_list.html'
    context_object_name = 'workloads'
    
    def get_queryset(self):
        project_progress_id = self.kwargs.get('progress_id')
        return SubActivityWorkload.objects.filter(
            project_progress_id=project_progress_id
        ).select_related('sub_activity', 'project_progress')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['progress_id'] = self.kwargs.get('progress_id')
        return context

@login_required
def add_workload(request, progress_id):
    project_progress = get_object_or_404(ProjectProgress, id=progress_id)
    
    if request.method == 'POST':
        form = SubActivityWorkloadForm(request.POST, project_progress=project_progress)
        if form.is_valid():
            workload = form.save(commit=False)
            workload.project_progress = project_progress
            workload.save()
            messages.success(request, "Workload added successfully!")
            return redirect('workload_detail', workload_id=workload.id)
    else:
        form = SubActivityWorkloadForm(project_progress=project_progress)
    
    return render(request, 'workload/workload_form.html', {
        'form': form,
        'project_progress': project_progress,
        'title': 'Add Workload'
    })

@login_required
def edit_workload(request, workload_id):
    workload = get_object_or_404(SubActivityWorkload, id=workload_id)
    project_progress = workload.project_progress
    
    if request.method == 'POST':
        form = SubActivityWorkloadForm(
            request.POST, 
            instance=workload, 
            project_progress=project_progress
        )
        if form.is_valid():
            form.save()
            messages.success(request, "Workload updated successfully!")
            return redirect('workload_detail', workload_id=workload.id)
    else:
        form = SubActivityWorkloadForm(
            instance=workload,
            project_progress=project_progress
        )
    
    return render(request, 'workload/workload_form.html', {
        'form': form,
        'workload': workload,
        'project_progress': project_progress,
        'title': 'Edit Workload'
    })

@login_required
def delete_workload(request, workload_id):
    workload = get_object_or_404(SubActivityWorkload, id=workload_id)
    project_progress = workload.project_progress
    
    if request.method == 'POST':
        workload.delete()
        messages.success(request, "Workload deleted successfully!")
        return redirect('workload_list', progress_id=project_progress.id)
    
    return render(request, 'workload/workload_confirm_list_delete.html', {
        'workload': workload
    })

@login_required
def workload_detail(request, workload_id):
    workload = get_object_or_404(
        SubActivityWorkload.objects.select_related(
            'project_progress', 'sub_activity', 'project_progress__vendor', 'project_progress__project'
        ),
        id=workload_id
    )

    # Get all progress records for this workload with proper ordering
    progress_records = SubActivityProgress.objects.filter(workload=workload).order_by('start_date')
    
    # Calculate time efficiency metrics
    time_metrics = {
        'expected_duration': workload.expected_duration or 0,
        'actual_duration': 0,
        'efficiency': 1.0,
        'status': 'not_started'
    }
    
    if progress_records.exists():
        # Calculate actual duration from first start to last end date
        first_record = progress_records.first()
        last_record = progress_records.last()
        if first_record and last_record:
            time_metrics['actual_duration'] = (last_record.end_date - first_record.start_date).days + 1
            
            # Calculate efficiency if we have expected duration
            if workload.expected_duration and time_metrics['actual_duration'] > 0:
                time_metrics['efficiency'] = round(float(workload.expected_duration) / time_metrics['actual_duration'], 2)
                
                # Determine status based on efficiency
                if last_record.progress == 100:  # If completed
                    if time_metrics['efficiency'] >= 1.0:
                        time_metrics['status'] = 'completed_on_time'
                    else:
                        time_metrics['status'] = 'completed_delayed'
                else:  # If in progress
                    expected_progress_rate = 1.0 / float(workload.expected_duration) if workload.expected_duration else 0
                    actual_progress_rate = float(last_record.progress) / 100.0 / time_metrics['actual_duration'] if time_metrics['actual_duration'] > 0 else 0
                    
                    if actual_progress_rate >= expected_progress_rate:
                        time_metrics['status'] = 'on_track'
                    else:
                        time_metrics['status'] = 'behind_schedule'
    
    if request.method == 'POST':
        formset = SubActivityProgressFormSet(request.POST, instance=workload)
        if formset.is_valid():
            instances = formset.save(commit=False)
            for instance in instances:
                instance.workload = workload
                instance.save()
            
            # Handle deleted forms
            for obj in formset.deleted_objects:
                obj.delete()
            
            # Recalculate project progress after saving
            workload.project_progress.calculate_progress()
            
            messages.success(request, "Progress records updated successfully!")
            return redirect('workload_detail', workload_id=workload.id)
        else:
            # Print form errors for debugging
            print(formset.errors)
    else:
        # Create formset with smarter initial data
        latest_progress = progress_records.last()
        
        # If there's existing progress, use the latest values as a starting point
        if latest_progress:
            initial_data = [{
                'progress': min(latest_progress.progress + 10, 100),  # Increment by 10%, max 100%
                'start_date': latest_progress.end_date,  # Start where the last one ended
                'end_date': latest_progress.end_date + timezone.timedelta(days=3)  # Default to 3 days later
            }]
        else:
            # Otherwise use project dates
            initial_data = [{
                'progress': 0,  # Default progress value
                'start_date': workload.project_progress.start_date,  # Use project start date as default
                'end_date': workload.project_progress.start_date + timezone.timedelta(days=3)  # Default to 3 days later
            }]
        
        formset = SubActivityProgressFormSet(instance=workload, initial=initial_data)
        
        # Make the formset support deletion if there are existing records
        if progress_records.exists():
            formset.can_delete = True

    # Calculate progress trend data for visualization
    trend_data = []
    cumulative_progress = 0
    for record in progress_records:
        cumulative_progress = max(cumulative_progress, record.progress)  # Progress should never decrease
        trend_data.append({
            'date': record.end_date.isoformat(),
            'progress': cumulative_progress,
            'duration': record.actual_duration
        })

    return render(request, 'workload/workload_detail.html', {
        'workload': workload,
        'progress_records': progress_records,
        'formset': formset,
        'time_metrics': time_metrics,
        'trend_data': trend_data,
        'latest_progress': progress_records.last().progress if progress_records.exists() else 0
    })

@login_required
def project_create(request):
    """View to create a new project"""
    if request.method == 'POST':
        form = ProjectForm(request.POST, user=request.user)
        if form.is_valid():
            project = form.save()
            messages.success(request, "Project created successfully!")
            return redirect('project_list')
    else:
        form = ProjectForm(user=request.user)
    
    return render(request, 'projects/project_form.html', {
        'form': form,
        'edit': False
    })

@login_required
def project_edit(request, pk):
    """View to edit an existing project"""
    project = get_object_or_404(Project, pk=pk)
    
    if request.method == 'POST':
        form = ProjectForm(request.POST, instance=project, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, "Project updated successfully!")
            return redirect('project_list')
    else:
        form = ProjectForm(instance=project, user=request.user)
    
    return render(request, 'projects/project_form.html', {
        'form': form,
        'project': project,
        'edit': True
    })
class Project_List(LoginRequiredMixin, ListView):
    """View to display a list of all projects"""
    model = Project
    template_name = 'projects/project_list.html'
    context_object_name = 'projects'
    paginate_by = 10
    
    def get_queryset(self):
        queryset = Project.objects.all().select_related('vendor', 'region')
        
        # Apply filters if provided
        status = self.request.GET.get('status')
        region = self.request.GET.get('region')
        project_type = self.request.GET.get('type')
        search_query = self.request.GET.get('search')
        
        if status:
            queryset = queryset.filter(status=status)
        if region:
            queryset = queryset.filter(region__name=region)
        if project_type:
            queryset = queryset.filter(type=project_type)
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) | 
                Q(vendor__name__icontains=search_query)
            )
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter choices to context
        context['regions'] = Region.objects.all()
        context['status_choices'] = Project.STATUS_CHOICES
        context['type_choices'] = Project.TYPE_CHOICES
        
        # Add selected filter values to context
        context['selected_status'] = self.request.GET.get('status', '')
        context['selected_region'] = self.request.GET.get('region', '')
        context['selected_type'] = self.request.GET.get('type', '')
        context['search_query'] = self.request.GET.get('search', '')
        
        return context

# Project Progress Detail View
def project_progress_detail(request, pk):
    progress = get_object_or_404(ProjectProgress, pk=pk)
    workloads = SubActivityWorkload.objects.filter(project_progress=progress)
    
    # Get timeline data for visualization
    timeline_data = progress.get_progress_timeline()
    
    # Get time performance metrics
    time_performance = progress.get_time_performance()
    
    # Calculate progress by main activity categories
    activity_progress = {}
    for workload in workloads:
        activity_name = workload.sub_activity.main_activity.title
        if activity_name not in activity_progress:
            activity_progress[activity_name] = {
                'total_weight': 0,
                'weighted_progress': 0
            }
        
        latest_progress = workload.progress_records.order_by('-end_date').first()
        if latest_progress:
            weight = workload.sub_activity.weight or 1
            activity_progress[activity_name]['total_weight'] += weight
            activity_progress[activity_name]['weighted_progress'] += (latest_progress.progress * weight)
    
    # Calculate percentages for each activity
    for activity in activity_progress:
        if activity_progress[activity]['total_weight'] > 0:
            activity_progress[activity]['percentage'] = round(
                activity_progress[activity]['weighted_progress'] / 
                activity_progress[activity]['total_weight'], 2
            )
        else:
            activity_progress[activity]['percentage'] = 0
    
    context = {
        'progress': progress,
        'workloads': workloads,
        'timeline_data': timeline_data,
        'time_performance': time_performance,
        'activity_progress': activity_progress,
    }
    return render(request, 'progress/progress_detail.html', context)



# 1. List View
def project_progress_list(request):
    progresses = ProjectProgress.objects.select_related('project', 'vendor', 'main_activity').order_by('-start_date')
    return render(request, 'progress/project_progress_list.html', {'progresses': progresses})

# 2. Create View
def create_project_progress(request):
    if request.method == 'POST':
        form = ProjectProgressForm(request.POST)
        if form.is_valid():
            project_progress = form.save()
            messages.success(request, "Project progress created successfully.")
            return redirect('project_progress_update', pk=project_progress.pk)
    else:
        form = ProjectProgressForm()
    return render(request, 'progress/project_progress_form.html', {'form': form})

# 3. Update View (ProjectProgress + related workloads + progress records)
def project_progress_update(request, pk):
    project_progress = get_object_or_404(ProjectProgress, pk=pk)
    workload_qs = SubActivityWorkload.objects.filter(project_progress=project_progress).select_related('sub_activity')
    
    SubActivityWorkloadFormSet = inlineformset_factory(
        ProjectProgress, SubActivityWorkload, form=SubActivityWorkloadForm,
        extra=1, can_delete=True
    )

    if request.method == 'POST':
        form = ProjectProgressForm(request.POST, instance=project_progress)
        workload_formset = SubActivityWorkloadFormSet(request.POST, instance=project_progress)

        if form.is_valid() and workload_formset.is_valid():
            form.save()
            workload_formset.save()
            project_progress.calculate_progress()
            messages.success(request, "Progress and workloads updated.")
            return redirect('project_progress_detail', pk=pk)
    else:
        form = ProjectProgressForm(instance=project_progress)
        workload_formset = SubActivityWorkloadFormSet(instance=project_progress)

    return render(request, 'progress/project_progress_form.html', {
        'form': form,
        'workload_formset': workload_formset,
        'project_progress': project_progress
    })

# 4. Detail View
def project_progress_detail(request, pk):
    progress = get_object_or_404(ProjectProgress, pk=pk)
    workloads = progress.workloads.select_related('sub_activity').prefetch_related('progress_records')

    timeline = progress.get_progress_timeline()
    time_performance = progress.get_time_performance()
    return render(request, 'progress/project_progress_detail.html', {
        'progress': progress,
        'workloads': workloads,
        'timeline': timeline,
        'time_performance': time_performance
    })

# 5. Delete View
def project_progress_delete(request, pk):
    progress = get_object_or_404(ProjectProgress, pk=pk)
    if request.method == 'POST':
        progress.delete()
        messages.success(request, "Project progress deleted successfully.")
        return redirect('project_progress_list')
    return render(request, 'progress/project_progress_confirm_delete.html', {'progress': progress})


# views.py
from django.shortcuts import render
from django.db.models import Count, Avg
from django.utils import timezone
from django.db.models.functions import TruncDay
from .models import Project, ProjectProgress, Region, Vendorsregistration

def dashboard_view(request):
    today = timezone.now().date()
    thirty_days_ago = today - timezone.timedelta(days=30)

    region_id = request.GET.get('region')
    contractor_type = request.GET.get('contractor_type')

    projects_qs = Project.objects.all()
    progress_qs = ProjectProgress.objects.all()

    if region_id:
        projects_qs = projects_qs.filter(region_id=region_id)
        progress_qs = progress_qs.filter(project__region_id=region_id)

    if contractor_type:
        projects_qs = projects_qs.filter(vendor__contractor_type=contractor_type)
        progress_qs = progress_qs.filter(vendor__contractor_type=contractor_type)
    if contractor_type:
        projects_qs = projects_qs.filter(vendor__contractor_type=contractor_type)
        progress_qs = progress_qs.filter(project__vendor__contractor_type=contractor_type)
    status_counts = list(
        projects_qs.values('status')
                .annotate(count=Count('id'))
                .order_by('status')
    )

    time_data = list(
        progress_qs
        .filter(start_date__gte=thirty_days_ago)
        .annotate(day=TruncDay('start_date'))
        .values('day')
        .annotate(avg_progress=Avg('progress_percentage'))
        .order_by('day')
    )

    completed_bar_data = list(
        projects_qs
        .filter(status='completed', start_date__gte=thirty_days_ago)
        .annotate(day=TruncDay('start_date'))
        .values('day')
        .annotate(count=Count('id'))
        .order_by('day')
    )

    regions = Region.objects.all()
    contractor_types = Vendorsregistration.CONTRACTOR_TYPE_CHOICES

    context = {
        'status_counts': status_counts,
        'time_data': time_data,
        'completed_bar_data': completed_bar_data,
        'regions': regions,
        'contractor_types': contractor_types,
        'selected_region': region_id,
        'selected_contractor_type': contractor_type,
    }
    return render(request, 'dashboard/dashboard.html', context)

# views.py
class VendorDocumentListView(LoginRequiredMixin, ListView):
    model = VendorDocument
    template_name = 'Documents/vendor_document_list.html'
    context_object_name = 'documents'

    def get_queryset(self):
        vendor_id = self.kwargs.get('vendor_id')
        if vendor_id:
            return VendorDocument.objects.filter(vendor_id=vendor_id).select_related('vendor')
        return VendorDocument.objects.all().select_related('vendor')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        vendor_id = self.kwargs.get('vendor_id')
        if vendor_id:
            context['vendor'] = Vendor.objects.get(id=vendor_id)
        context['title'] = "Vendor Documents"
        return context


# views.py
from .models import Vendor, VendorDocument
from .forms import VendorDocumentForm

def add_vendor_document(request):
    if request.method == 'POST':
        form = VendorDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return redirect('all_vendor_documents')
    else:
        form = VendorDocumentForm()
    
    vendors = Vendor.objects.select_related('vendor_registration').all()
    return render(request, 'documents/vendor_document_form.html', {'form': form, 'vendors': vendors, 'title': 'Upload Vendor Document'})


@login_required
def edit_vendor_document(request, document_id):
    document = get_object_or_404(VendorDocument, id=document_id)

    if request.method == 'POST':
        form = VendorDocumentForm(request.POST, request.FILES, instance=document)
        if form.is_valid():
            form.save()
            messages.success(request, "Document updated successfully!")
            return redirect('vendor_document_list', vendor_id=document.vendor.id)
    else:
        form = VendorDocumentForm(instance=document)

    return render(request, 'Documents/vendor_document_form.html', {
        'form': form,
        'title': 'Edit Vendor Document',
        'vendor': document.vendor
    })


@login_required
def delete_vendor_document(request, document_id):
    document = get_object_or_404(VendorDocument, id=document_id)
    vendor_id = document.vendor.id

    if request.method == 'POST':
        document.delete()
        messages.success(request, "Document deleted successfully!")
        return redirect('vendor_document_list', vendor_id=vendor_id)

    return render(request, 'Documents/vendor_document_confirm_delete.html', {
        'document': document
    })

# ProjectVendorsHistory Views
class VendorHistoryListView(LoginRequiredMixin, ListView):
    model = ProjectVendorsHistory
    template_name = 'vendor/vendor_history_list.html'
    context_object_name = 'histories'
    
    def get_queryset(self):
        vendor_id = self.kwargs.get('vendor_id')
        if vendor_id:
            return ProjectVendorsHistory.objects.filter(
                vendor_id=vendor_id
            ).select_related('project', 'vendor')
        return ProjectVendorsHistory.objects.all().select_related('project', 'vendor')

@login_required
def add_vendor_history(request, vendor_id=None, project_id=None):
    initial = {}
    if vendor_id:
        vendor = get_object_or_404(Vendor, id=vendor_id)
        initial['vendor'] = vendor
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        initial['project'] = project
    
    if request.method == 'POST':
        form = ProjectVendorsHistoryForm(request.POST)
        if form.is_valid():
            history = form.save()
            messages.success(request, "Vendor history record added successfully!")
            return redirect('vendor_history_detail', history_id=history.id)
    else:
        form = ProjectVendorsHistoryForm(initial=initial)
    
    return render(request, 'vendor/vendor_history_form.html', {
        'form': form,
        'title': 'Add Vendor History'
    })

@login_required
def edit_vendor_history(request, history_id):
    history = get_object_or_404(ProjectVendorsHistory, id=history_id)
    
    if request.method == 'POST':
        form = ProjectVendorsHistoryForm(request.POST, instance=history)
        if form.is_valid():
            form.save()
            messages.success(request, "Vendor history updated successfully!")
            return redirect('vendor_history_detail', history_id=history.id)
    else:
        form = ProjectVendorsHistoryForm(instance=history)
    
    return render(request, 'vendor/vendor_history_form.html', {
        'form': form,
        'history': history,
        'title': 'Edit Vendor History'
    })

@login_required
def vendor_history_detail(request, history_id):
    history = get_object_or_404(
        ProjectVendorsHistory.objects.select_related('vendor', 'project'),
        id=history_id
    )
    
    return render(request, 'vendor/vendor_history_detail.html', {
        'history': history
    })






# Report Views
def vendor_performance_report(request):
    vendors = Vendor.objects.all()
    
    # Calculate performance metrics for each vendor
    for vendor in vendors:
        vendor.completed_projects = ProjectVendorsHistory.objects.filter(
            vendor=vendor, 
            status='completed'
        ).count()
        
        vendor.ongoing_projects = ProjectProgress.objects.filter(
            vendor=vendor, 
            status='in_progress'
        ).count()
        
        # Calculate average completion time
        histories = ProjectVendorsHistory.objects.filter(vendor=vendor)
        if histories.exists():
            total_time_factor = sum(h.time_factor for h in histories if h.time_factor)
            vendor.avg_time_factor = total_time_factor / histories.count()
        else:
            vendor.avg_time_factor = 0
    
    context = {
        'vendors': vendors,
    }
    return render(request, 'reports/vendor_performance.html', context)

def project_status_report(request):
    projects = Project.objects.all()
    
    # Calculate status metrics for each project
    for project in projects:
        project.progress_reports = ProjectProgress.objects.filter(project=project)
        
        if project.progress_reports.exists():
            # Calculate overall progress percentage
            total_percentage = sum(p.progress_percentage for p in project.progress_reports)
            project.overall_progress = total_percentage / project.progress_reports.count()
            
            # Get status counts
            project.completed = project.progress_reports.filter(status='completed').count()
            project.in_progress = project.progress_reports.filter(status='in_progress').count()
            project.delayed = project.progress_reports.filter(status='delayed').count()
        else:
            project.overall_progress = 0
            project.completed = 0
            project.in_progress = 0
            project.delayed = 0
    
    context = {
        'projects': projects,
    }
    return render(request, 'reports/project_status.html', context)

def progress_summary_report(request):
    # Get overall statistics
    total_projects = Project.objects.count()
    total_vendors = Vendor.objects.count()
    total_progress_reports = ProjectProgress.objects.count()
    
    # Status breakdown
    completed = ProjectProgress.objects.filter(status='completed').count()
    in_progress = ProjectProgress.objects.filter(status='in_progress').count()
    delayed = ProjectProgress.objects.filter(status='delayed').count()
    
    # Recent activities
    recent_progress = ProjectProgress.objects.order_by('-updated_at')[:10]
    recent_workloads = SubActivityWorkload.objects.order_by('-created_at')[:10]
    
    context = {
        'total_projects': total_projects,
        'total_vendors': total_vendors,
        'total_progress_reports': total_progress_reports,
        'completed': completed,
        'in_progress': in_progress,
        'delayed': delayed,
        'recent_progress': recent_progress,
        'recent_workloads': recent_workloads,
    }
    return render(request, 'reports/progress_summary.html', context)