{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm rounded-3 p-4">
    <h2 class="mb-4 text-primary">{% if form.instance.pk %}Edit{% else %}Create{% endif %} Project Progress</h2>

    <form method="post">
      {% csrf_token %}
      
      {% if form.errors %}
        <div class="alert alert-danger">
          Please correct the errors below.
        </div>
      {% endif %}
      
      <!-- Main form fields in two columns -->
      <div class="row">
        <div class="col-md-6 mb-3">
          <label for="{{ form.project.id_for_label }}" class="form-label fw-bold">Project</label>
          {{ form.project }}
          {% if form.project.errors %}
            <div class="text-danger small">{{ form.project.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-md-6 mb-3">
          <label for="{{ form.vendor.id_for_label }}" class="form-label fw-bold">Vendor</label>
          {{ form.vendor }}
          {% if form.vendor.errors %}
            <div class="text-danger small">{{ form.vendor.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-md-6 mb-3">
          <label for="{{ form.main_activity.id_for_label }}" class="form-label fw-bold">Main Activity</label>
          {{ form.main_activity }}
          {% if form.main_activity.errors %}
            <div class="text-danger small">{{ form.main_activity.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-md-6 mb-3">
          <label for="{{ form.status.id_for_label }}" class="form-label fw-bold">Status</label>
          {{ form.status }}
          {% if form.status.errors %}
            <div class="text-danger small">{{ form.status.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-md-6 mb-3">
          <label for="{{ form.start_date.id_for_label }}" class="form-label fw-bold">Start Date</label>
          {{ form.start_date }}
          {% if form.start_date.errors %}
            <div class="text-danger small">{{ form.start_date.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-md-6 mb-3">
          <label for="{{ form.completion_date.id_for_label }}" class="form-label fw-bold">Expected Completion Date</label>
          {{ form.completion_date }}
          {% if form.completion_date.errors %}
            <div class="text-danger small">{{ form.completion_date.errors }}</div>
          {% endif %}
        </div>
        
        <div class="col-12 mb-3">
          <label for="{{ form.remark.id_for_label }}" class="form-label fw-bold">Remarks</label>
          {{ form.remark }}
          {% if form.remark.errors %}
            <div class="text-danger small">{{ form.remark.errors }}</div>
          {% endif %}
        </div>
      </div>
      
      <div class="d-flex justify-content-between mt-3">
        <a href="{% url 'project_progress_list' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Back to List
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save Progress
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form elements
    const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
    formControls.forEach(element => {
      element.classList.add('form-control');
    });
    
    // Project change handler to filter vendors
    const projectSelect = document.getElementById('{{ form.project.id_for_label }}');
    const vendorSelect = document.getElementById('{{ form.vendor.id_for_label }}');
    
    if (projectSelect && vendorSelect) {
      projectSelect.addEventListener('change', function() {
        const projectId = this.value;
        if (projectId) {
          fetch(`/api/project/${projectId}/vendors/`)
            .then(response => response.json())
            .then(data => {
              // Clear current options
              vendorSelect.innerHTML = '<option value="">---------</option>';
              
              // Add new options
              data.vendors.forEach(vendor => {
                const option = document.createElement('option');
                option.value = vendor.id;
                option.textContent = vendor.name;
                vendorSelect.appendChild(option);
              });
            });
        }
      });
    }
    
    // Main activity change handler
    const mainActivitySelect = document.getElementById('{{ form.main_activity.id_for_label }}');
    if (mainActivitySelect) {
      mainActivitySelect.addEventListener('change', function() {
        // You can add logic here to handle main activity changes
      });
    }
  });
</script>
{% endblock %}