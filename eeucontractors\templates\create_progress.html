{% extends 'base.html' %} {% block content %}
<h2>Add Project Progress</h2>

<form method="post" id="progress-form">
  {% csrf_token %} {{ form.as_p }}

  <div id="subactivity-formset">
    <!-- Formset will be loaded here dynamically -->
  </div>

  <button type="submit" class="btn btn-primary">Save</button>
  <a href="{% url 'progress_list' %}" class="btn btn-secondary">Cancel</a>
</form>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
  $(document).ready(function () {
    $("#id_main_activity").change(function () {
      var main_activity_id = $(this).val();
      if (main_activity_id) {
        $.ajax({
          url: "{% url 'load_subactivities' %}",
          data: {
            main_activity_id: main_activity_id,
          },
          success: function (data) {
            $("#subactivity-formset").html(data.formset_html);
          },
        });
      }
    });
  });
</script>

{% endblock %} {% comment %} {% extends 'base.html' %} {% block content %}
<h2>Add Project Progress</h2>

<form method="post">
  {% csrf_token %} {{ form.as_p }}

  <h3>Sub Activity Progress</h3>
  {{ formset.management_form }}
  <table class="table">
    <tr>
      <th>Sub Activity</th>
      <th>Progress %</th>
      <th>Start Date</th>
      <th>End Date</th>
    </tr>
    {% for subform in formset %}
    <tr>
      <td>{{ subform.sub_activity }}</td>
      <td>{{ subform.progress }}</td>
      <td>{{ subform.start_date }}</td>
      <td>{{ subform.end_date }}</td>
    </tr>
    {% endfor %}
  </table>

  <button type="submit" class="btn btn-primary">Save</button>
</form>
{% endblock %} {% endcomment %}
