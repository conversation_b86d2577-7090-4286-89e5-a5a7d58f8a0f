{% extends 'base.html' %}
{% load static %}
{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm rounded-3 p-4">
    <h2 class="mb-4 text-primary">Create Project Progress</h2>

    <form method="post">
      {% csrf_token %}
      
      <!-- Main form fields in two columns -->
      <div class="row">
        {% for field in form %}
          <div class="col-md-6 mb-3">
            <label for="{{ field.id_for_label }}" class="form-label fw-bold">{{ field.label }}</label>
            {% if field.name == 'remark' %}
              <textarea name="{{ field.name }}" id="{{ field.id_for_label }}" class="form-control" rows="2">{{ field.value|default:'' }}</textarea>
            {% else %}
              {{ field }}
            {% endif %}
            {% if field.errors %}
              <div class="text-danger small">{{ field.errors }}</div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
      
      {{ formset.management_form }}
      
      <!-- Sub-activity progress cards -->
      <h4 class="mt-0 mb-3">Sub-Activity Progress</h4>
      <div class="row">
        {% for subform in formset %}
          <div class="col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
              <div class="card-header bg-light">
                <h5 class="text-success mb-0">
                  {{ subform.instance.sub_activity_workload.sub_activity.name }}
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  {% for field in subform %}
                    {% if not field.is_hidden %}
                      <div class="col-md-6 mb-2">
                        <label for="{{ field.id_for_label }}" class="form-label small fw-bold">{{ field.label }}</label>
                        {% if field.name|slice:"-7:" == "_remark" %}
                          <textarea name="{{ field.name }}" id="{{ field.id_for_label }}" class="form-control" rows="2">{{ field.value|default:'' }}</textarea>
                        {% else %}
                          {{ field }}
                        {% endif %}
                        {% if field.errors %}
                          <div class="text-danger small">{{ field.errors }}</div>
                        {% endif %}
                      </div>
                    {% else %}
                      {{ field }}
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>

      <div class="mt-4 d-flex justify-content-between">
        <a href="{% url 'project_progress_list' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Cancel
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form elements
    const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
    formControls.forEach(element => {
      element.classList.add('form-control');
    });
    
    // Add Bootstrap classes to checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      checkbox.classList.add('form-check-input');
    });
  });
</script>
{% endblock %}
