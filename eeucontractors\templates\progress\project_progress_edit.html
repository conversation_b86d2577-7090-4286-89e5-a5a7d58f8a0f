{% comment %}
<form method="post" action="{% url 'edit_project_progress' form.instance.pk %}">
  {% csrf_token %}
  <div class="modal-header">
    <h5 class="modal-title" id="modal-progress-label">Edit Project Progress</h5>
    <button
      type="button"
      class="btn-close"
      data-bs-dismiss="modal"
      aria-label="Close"
    ></button>
  </div>
  <div class="modal-body">
    {% for field in form %}
    <div class="mb-3">
      {{ field.label_tag }} {{ field }} {% if field.errors %}
      <div class="text-danger">{{ field.errors|striptags }}</div>
      {% endif %}
    </div>
    {% endfor %}
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
      Cancel
    </button>
    <button type="submit" class="btn btn-primary">Save changes</button>
  </div>
</form>
{% endcomment %}
