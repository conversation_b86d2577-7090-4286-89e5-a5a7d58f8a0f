{% extends 'base.html' %}
{% load static %}
{% block content %}
<div class="container mt-4">
  <h2>{{ title }}</h2>

  <form method="post" novalidate>
    {% csrf_token %}
    {{ form.non_field_errors }}

    {% for field in form %}
      <div class="form-group" id="field-wrapper-{{ field.name }}">
        {{ field.label_tag }}
        {{ field }}
        {% if field.help_text %}
          <small class="form-text text-muted">{{ field.help_text }}</small>
        {% endif %}
        {% for error in field.errors %}
          <div class="text-danger">{{ error }}</div>
        {% endfor %}
      </div>
    {% endfor %}

    <button type="submit" class="btn btn-primary">Save</button>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
  const statusField = document.getElementById('id_status');
  const delayFieldWrapper = document.getElementById('field-wrapper-cause_of_delay_or_suspension');

  function toggleDelayReason() {
    if (statusField.value === 'delayed' || statusField.value === 'suspended') {
      delayFieldWrapper.style.display = 'block';
    } else {
      delayFieldWrapper.style.display = 'none';
      document.getElementById('id_cause_of_delay_or_suspension').value = '';
    }
  }

  if (statusField && delayFieldWrapper) {
    statusField.addEventListener('change', toggleDelayReason);
    toggleDelayReason(); // Initial state
  }
});
</script>
{% endblock %}
