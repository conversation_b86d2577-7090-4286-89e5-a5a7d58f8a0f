from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    Vendorsregistration, Region, Vendor, Project, MainActivity, SubActivity,
    ProjectProgress, SubActivityWorkload, SubActivityProgress,
    ProjectVendorsHistory, VendorDocument
)

from django.contrib import admin
from .models import MainActivity, SubActivity

class SubActivityInline(admin.TabularInline):
    model = SubActivity
    extra = 0

@admin.register(MainActivity)
class MainActivityAdmin(admin.ModelAdmin):
    inlines = [SubActivityInline]

@admin.register(SubActivity)
class SubActivityAdmin(admin.ModelAdmin):
    list_display = ['main_activity', 'name', 'order', 'weight', 'base_duration']
    list_filter = ['main_activity']
# 1. Admin for Region
@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ('name', 'region_code')
    search_fields = ('name', 'region_code')
    ordering = ('name',)

# 2. Admin for Vendorsregistration
@admin.register(Vendorsregistration)
class VendorsregistrationAdmin(admin.ModelAdmin):
    list_display = ('contractor_name', 'contractor_type', 'region', 'identification_number', 'is_registered', 'registration_date')
    list_filter = ('contractor_type', 'region', 'is_registered')
    search_fields = ('contractor_name', 'identification_number', 'registrar_name')
    ordering = ('registration_date',)
    readonly_fields = ('identification_number', 'registration_date')

# 3. Admin for Vendor
@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ('name', 'level', 'type', 'identification_number', 'email', 'phone_number', 'year', 'experience', 'status')
    list_filter = ('type', 'level', 'status')
    search_fields = ('name', 'identification_number', 'email', 'phone_number')
    ordering = ('name',)


# 6. Inline for SubActivityWorkload (for ProjectProgress admin)
class SubActivityWorkloadInline(admin.TabularInline):
    model = SubActivityWorkload
    extra = 0

# 7. Admin for ProjectProgress
@admin.register(ProjectProgress)
class ProjectProgressAdmin(admin.ModelAdmin):
    list_display = ('project', 'vendor', 'main_activity', 'progress_percentage', 'status', 'start_date', 'completion_date', 'updated_by')
    list_filter = ('status', 'main_activity', 'start_date')
    search_fields = ('project__name', 'vendor__name')
    ordering = ('-start_date',)
    inlines = [SubActivityWorkloadInline]

# 8. Admin for SubActivityProgress
@admin.register(SubActivityProgress)
class SubActivityProgressAdmin(admin.ModelAdmin):
    list_display = ('workload', 'progress', 'start_date', 'end_date')
    list_filter = ('workload__sub_activity',)
    search_fields = ('workload__sub_activity__name',)

# 9. Admin for Project
@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'region', 'vendor', 'level', 'type', 'main_activity', 'status', 'approved')
    list_filter = ('status', 'type', 'region', 'main_activity', 'approved')
    search_fields = ('name', 'vendor__name')
    ordering = ('name',)

# 10. Admin for ProjectVendorsHistory
@admin.register(ProjectVendorsHistory)
class ProjectVendorsHistoryAdmin(admin.ModelAdmin):
    list_display = ('vendor', 'project', 'status', 'start_date', 'end_date', 'cause_of_delay_or_suspension')
    list_filter = ('status', 'cause_of_delay_or_suspension')
    search_fields = ('vendor__name', 'project__name')
    ordering = ('-start_date',)

# 11. Admin for VendorDocument
@admin.register(VendorDocument)
class VendorDocumentAdmin(admin.ModelAdmin):
    list_display = ('vendor', 'document_type', 'file', 'expiry_date', 'uploaded_at')
    list_filter = ('document_type',)
    search_fields = ('vendor__name',)
    ordering = ('-uploaded_at',)

# Register SubActivityWorkload in the admin
@admin.register(SubActivityWorkload)
class SubActivityWorkloadAdmin(admin.ModelAdmin):
    list_display = ('sub_activity', 'project_progress', 'quantity', 'expected_duration')
    list_filter = ('project_progress__project', 'sub_activity')
    search_fields = ('sub_activity__name', 'project_progress__project__name')
from django.contrib import admin

# 🔹 Tab title (shown in browser tab)
admin.site.site_title = "EEU Admin Portal"

# 🔸 Top-left header
admin.site.site_header = "Well Come to Admin Page"

# 🟢 Main dashboard page title
admin.site.index_title = "Project Progress Management System"
