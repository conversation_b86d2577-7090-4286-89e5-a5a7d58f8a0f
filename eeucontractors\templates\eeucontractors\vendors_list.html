{% extends "base.html" %}
{% load static %}

{% block title %}Vendors Registration List{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
    .filter-card {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
    }
    .badge-registered {
        background-color: #28a745;
    }
    .badge-not-registered {
        background-color: #dc3545;
    }
    .vendor-link {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
    }
    .vendor-link:hover {
        text-decoration: underline;
    }
    .count-badge {
        font-size: 0.8rem;
        margin-left: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">የኮንትራክተሮች ምዝገባ ዝርዝር</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>የተመዘገበው ስም</th>
                                    <th>የኮንትራክተሩ አይነት</th>
                                    <th>የመወዳደሪያ መለያ ቁጥር</th>
                                    <th>ተመዝግቧል?</th>
                                    <th>የመዝጋቢ ሰራተኛው ስም</th>
                                    <th>Created at</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for vendor in vendors %}
                                <tr>
                                    <td><a href="{% url 'vendor_detail' vendor.id %}" class="vendor-link">{{ vendor.name }}</a></td>
                                    <td>{{ vendor.contractor_type }}</td>
                                    <td>{{ vendor.registration_number }}</td>
                                    <td>
                                        {% if vendor.is_registered %}
                                        <span class="badge badge-registered">Yes</span>
                                        {% else %}
                                        <span class="badge badge-not-registered">No</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ vendor.registrar_name }}</td>
                                    <td>{{ vendor.created_at|date:"M d, Y, g:i a" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">No vendors registered yet.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-muted">{{ total_count }} vendorsregistration</span>
                        </div>
                        
                        {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if contractor_type != 'All' %}&contractor_type={{ contractor_type }}{% endif %}{% if is_registered != 'All' %}&is_registered={{ is_registered }}{% endif %}{% if date_filter != 'Any date' %}&date_filter={{ date_filter }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if contractor_type != 'All' %}&contractor_type={{ contractor_type }}{% endif %}{% if is_registered != 'All' %}&is_registered={{ is_registered }}{% endif %}{% if date_filter != 'Any date' %}&date_filter={{ date_filter }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if contractor_type != 'All' %}&contractor_type={{ contractor_type }}{% endif %}{% if is_registered != 'All' %}&is_registered={{ is_registered }}{% endif %}{% if date_filter != 'Any date' %}&date_filter={{ date_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if contractor_type != 'All' %}&contractor_type={{ contractor_type }}{% endif %}{% if is_registered != 'All' %}&is_registered={{ is_registered }}{% endif %}{% if date_filter != 'Any date' %}&date_filter={{ date_filter }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if contractor_type != 'All' %}&contractor_type={{ contractor_type }}{% endif %}{% if is_registered != 'All' %}&is_registered={{ is_registered }}{% endif %}{% if date_filter != 'Any date' %}&date_filter={{ date_filter }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card filter-card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filter</h5>
                    <small class="text-muted">Show counts</small>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'vendors_list' %}">
                        <div class="form-group">
                            <label><strong>By የኮንትራክተሩ አይነት</strong></label>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="type-all" name="contractor_type" value="All" class="custom-control-input" {% if contractor_type == 'All' %}checked{% endif %}>
                                <label class="custom-control-label" for="type-all">All <span class="badge badge-secondary count-badge">{{ total_count }}</span></label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="type-contractor" name="contractor_type" value="Contractor" class="custom-control-input" {% if contractor_type == 'Contractor' %}checked{% endif %}>
                                <label class="custom-control-label" for="type-contractor">Contractor <span class="badge badge-secondary count-badge">{{ contractor_count }}</span></label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="type-enterprise" name="contractor_type" value="Enterprise" class="custom-control-input" {% if contractor_type == 'Enterprise' %}checked{% endif %}>
                                <label class="custom-control-label" for="type-enterprise">Enterprise <span class="badge badge-secondary count-badge">{{ enterprise_count }}</span></label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="type-new-enterprise" name="contractor_type" value="New Enterprise" class="custom-control-input" {% if contractor_type == 'New Enterprise' %}checked{% endif %}>
                                <label class="custom-control-label" for="type-new-enterprise">New Enterprise <span class="badge badge-secondary count-badge">{{ new_enterprise_count }}</span></label>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <label><strong>By ተመዝግቧል?</strong></label>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="registered-all" name="is_registered" value="All" class="custom-control-input" {% if is_registered == 'All' %}checked{% endif %}>
                                <label class="custom-control-label" for="registered-all">All</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="registered-yes" name="is_registered" value="Yes" class="custom-control-input" {% if is_registered == 'Yes' %}checked{% endif %}>
                                <label class="custom-control-label" for="registered-yes">Yes</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="registered-no" name="is_registered" value="No" class="custom-control-input" {% if is_registered == 'No' %}checked{% endif %}>
                                <label class="custom-control-label" for="registered-no">No</label>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <label><strong>By created at</strong></label>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="date-any" name="date_filter" value="Any date" class="custom-control-input" {% if date_filter == 'Any date' %}checked{% endif %}>
                                <label class="custom-control-label" for="date-any">Any date</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="date-today" name="date_filter" value="Today" class="custom-control-input" {% if date_filter == 'Today' %}checked{% endif %}>
                                <label class="custom-control-label" for="date-today">Today</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="date-week" name="date_filter" value="Past 7 days" class="custom-control-input" {% if date_filter == 'Past 7 days' %}checked{% endif %}>
                                <label class="custom-control-label" for="date-week">Past 7 days</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="date-month" name="date_filter" value="This month" class="custom-control-input" {% if date_filter == 'This month' %}checked{% endif %}>
                                <label class="custom-control-label" for="date-month">This month</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="date-year" name="date_filter" value="This year" class="custom-control-input" {% if date_filter == 'This year' %}checked{% endif %}>
                                <label class="custom-control-label" for="date-year">This year</label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary mt-4">Apply Filters</button>
                        <a href="{% url 'vendors_list' %}" class="btn btn-outline-secondary mt-4 ml-2">Clear Filters</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}