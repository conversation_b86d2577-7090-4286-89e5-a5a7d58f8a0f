{% extends "base.html" %}
{% load static %}
{% block title %}Vendor Documents{% endblock %}

{% block content %}
<div class="container mt-4" style="max-width:900px;">
  <h2 class="mb-4">
    {% if vendor_id %}
      Documents for Vendor:
      <span class="text-primary">
        {% if documents and documents|length > 0 %}
          {{ documents.0.vendor.name }}
        {% else %}
          Unknown
        {% endif %}
      </span>
    {% else %}
      All Vendor Documents
    {% endif %}
  </h2>

  <div class="mb-3">
    {% if vendor_id %}
      <a href="{% url 'add_vendor_document' vendor_id=vendor_id %}" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Add Document
      </a>
      <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary ms-2">View All Documents</a>
    {% else %}
      <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary">Refresh List</a>
    {% endif %}
  </div>

  {% if documents %}
    <div class="table-responsive">
      <table class="table table-hover align-middle">
        <thead class="table-light">
          <tr>
            <th>Vendor</th>
            <th>Type</th>
            <th>Description</th>
            <th>Expiry Date</th>
            <th>Uploaded At</th>
            <th>File</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for doc in documents %}
          <tr>
            <td>{{ doc.vendor.name }}</td>
            <td>{{ doc.get_document_type_display }}</td>
            <td>{{ doc.description|default:"-" }}</td>
            <td>{{ doc.expiry_date|date:"M d, Y" }}</td>
            <td>{{ doc.uploaded_at|date:"M d, Y H:i" }}</td>
            <td>
              {% if doc.file %}
                <a href="{{ doc.file.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                  View
                </a>
              {% else %}
                -
              {% endif %}
            </td>
            <td>
              <a href="{% url 'edit_vendor_document' document_id=doc.id %}" class="btn btn-sm btn-warning" title="Edit">
                <i class="bi bi-pencil-square"></i>
              </a>
              <a href="{% url 'delete_vendor_document' document_id=doc.id %}" class="btn btn-sm btn-danger ms-1" title="Delete">
                <i class="bi bi-trash"></i>
              </a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <p class="text-muted fst-italic">No documents found.</p>
  {% endif %}
</div>
{% endblock %}
