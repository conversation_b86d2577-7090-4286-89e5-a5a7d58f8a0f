{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
      <h2 class="mb-0">Project Status Report</h2>
    </div>
    <div class="card-body">
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-light">
            <div class="card-body text-center">
              <h3 class="display-4">{{ projects.count }}</h3>
              <p class="lead">Total Projects</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body text-center">
              <h3 class="display-4">{{ projects|dictsortby:"completed"|last.completed|default:"0" }}</h3>
              <p class="lead">Completed</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body text-center">
              <h3 class="display-4">{{ projects|dictsortby:"in_progress"|last.in_progress|default:"0" }}</h3>
              <p class="lead">In Progress</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-warning text-dark">
            <div class="card-body text-center">
              <h3 class="display-4">{{ projects|dictsortby:"delayed"|last.delayed|default:"0" }}</h3>
              <p class="lead">Delayed</p>
            </div>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th>Project Name</th>
              <th>Region</th>
              <th>Start Date</th>
              <th>Overall Progress</th>
              <th>Status Breakdown</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for project in projects %}
            <tr>
              <td>{{ project.name }}</td>
              <td>{{ project.region.name }}</td>
              <td>{{ project.start_date|date:"M d, Y" }}</td>
              <td>
                <div class="progress" style="height: 20px;">
                  <div class="progress-bar bg-success" 
                       role="progressbar" 
                       style="width: {{ project.overall_progress }}%;" 
                       aria-valuenow="{{ project.overall_progress }}" 
                       aria-valuemin="0" 
                       aria-valuemax="100">
                    {{ project.overall_progress|floatformat:0 }}%
                  </div>
                </div>
              </td>
              <td>
                <div class="d-flex justify-content-between">
                  <span class="badge bg-success">{{ project.completed }}</span>
                  <span class="badge bg-primary">{{ project.in_progress }}</span>
                  <span class="badge bg-warning">{{ project.delayed }}</span>
                </div>
              </td>
              <td>
                <a href="{% url 'project_edit' pk=project.id %}" class="btn btn-sm btn-primary">
                  <i class="bi bi-eye"></i> View
                </a>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="6" class="text-center">No project data available</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock %}