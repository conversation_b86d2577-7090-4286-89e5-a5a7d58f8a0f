{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Workloads for {% if workloads %}{{ workloads.0.project_progress.project.name }}{% else %}Project{% endif %}</h2>
    <a href="{% url 'add_workload' progress_id=progress_id %}" class="btn btn-primary">
      <i class="bi bi-plus-circle"></i> Add Workload
    </a>
  </div>

  {% if messages %}
    <div class="messages">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">{{ message }}</div>
      {% endfor %}
    </div>
  {% endif %}

  {% if workloads %}
    <div class="table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-dark">
          <tr>
            <th>Sub Activity</th>
            <th>Quantity</th>
            <th>Expected Duration (days)</th>
            <th>Progress</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for workload in workloads %}
            <tr>
              <td>{{ workload.sub_activity.name }}</td>
              <td>{{ workload.quantity }} units</td>
              <td>{{ workload.expected_duration }}</td>
              <td>
                {% with progress_records=workload.progress_records.all %}
                  {% if progress_records %}
                    {% with latest=progress_records.last %}
                      <div class="progress">
                        {% with progress_value=latest.progress|floatformat:0 %}
                        <div class="progress-bar bg-success" 
                             style="width: {{ progress_value }}%;" 
                             role="progressbar" 
                             aria-valuenow="{{ progress_value }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                          {{ progress_value }}%
                        </div>
                        {% endwith %}
                      </div>
                    {% endwith %}
                  {% else %}
                    <span class="badge bg-warning">No progress recorded</span>
                  {% endif %}
                {% endwith %}
              </td>
              <td>
                <div class="btn-group" role="group">
                  <a href="{% url 'workload_detail' workload_id=workload.id %}" class="btn btn-sm btn-info">
                    <i class="bi bi-eye"></i> View
                  </a>
                  <a href="{% url 'edit_workload' workload_id=workload.id %}" class="btn btn-sm btn-warning">
                    <i class="bi bi-pencil"></i> Edit
                  </a>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-info">
      No workloads found for this project progress. Click "Add Workload" to create one.
    </div>
  {% endif %}
</div>
{% endblock %}













