<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>EEU Contractor System - Login</title>

  <!-- Bootstrap & Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet"/>

  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #4ca1af;
    }

    body {
      font-family: "Times New Roman", Times, serif;
      background-color:#FAFAFA;
      background-size: 400% 400%;
      animation: bodyGradientAnimation 15s ease infinite;
      margin: 0;
      padding-top: 70px;
    }

    .navbar {
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .navbar-brand img {
      height: 50px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .navbar-brand span {
      color: #fff;
      font-weight: bold;
      font-size: 1.4rem;
    }

    .welcome-banner {
      text-align: center;
      color: #fff;
      font-size: 2rem;
      font-weight: bold;
      margin: 30px auto 10px;
      text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
    }

    .login-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: calc(100vh - 150px);
    }

    .login-card {
      background: rgba(255, 255, 255, 0.96);
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      width: 900px;
      max-width: 95%;
    }

    .login-image {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      padding: 40px;
    }

    .login-image img {
      max-width: 80px;
      border-radius: 50%;
      margin-bottom: 20px;
    }

    .system-name {
      font-size: 1.8rem;
      font-weight: bold;
      margin-bottom: 15px;
      text-align: center;
    }

    .system-description {
      opacity: 0.9;
      max-width: 80%;
      text-align: center;
    }

    .login-form {
      padding: 40px;
    }

    .login-form h2 {
      color: var(--primary-color);
      margin-bottom: 30px;
      font-weight: bold;
    }

    .form-control {
      border-radius: 8px;
      padding: 12px 15px;
      border: 1px solid #e0e0e0;
      margin-bottom: 20px;
    }

    .form-control:focus {
      box-shadow: 0 0 0 3px rgba(76, 161, 175, 0.25);
      border-color: var(--secondary-color);
    }

    .btn-primary {
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      border: none;
      border-radius: 8px;
      padding: 12px 20px;
      font-weight: bold;
      transition: all 0.3s;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
    }

    .login-footer {
      font-size: 0.9rem;
      color: #6c757d;
      text-align: center;
      margin-top: 20px;
    }
    .gradient-text {
  background: linear-gradient(to bottom, orange, green);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .animated {
      animation: fadeIn 0.8s ease-out forwards;
    }

    .delay-1 { animation-delay: 0.2s; }
    .delay-2 { animation-delay: 0.4s; }
    .delay-3 { animation-delay: 0.6s; }

    .gradient-text1 {
  background: linear-gradient(to bottom, orange 60%, green 40%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

  </style>
</head>
<body>

  <!-- NAVIGATION HEADER -->
  <nav class="navbar fixed-top navbar-expand-lg">
    <div class="container-fluid px-4">
      <a class="navbar-brand d-flex align-items-center" href="#">
        <img src="/static/images/eeu_logo.png" alt="Logo" />
        {% comment %} <span>EEU Project Progress System</span> {% endcomment %}
          <span class="gradient-text1">EEU Project Progress System</span>

      </a>
    </div>
  </nav>

  <!-- WELCOME TEXT -->
  <div class="welcome-banner">
    {% comment %} WEll Come To EEU Project Progress Management System {% endcomment %}
      <span class="gradient-text1" style="font-size: 2rem;">Welcome to EEU Project Progress Management System</span>

  </div>

  <!-- LOGIN SECTION -->
  <div class="login-container">
    <div class="login-card row g-0">
      <div class="col-md-6 login-image">
        <div class="animated">
          <img src="/static/images/eeu_logo.png" alt="EEU Logo" class="img-fluid" />
        </div>
        <div class="system-name animated delay-1"><span class="gradient-text">Projects Progress Management System<span></div>
        
      </div>
      <div class="col-md-6 login-form">
        <div class="animated delay-1">
          {% comment %} <h2><i class="bi bi-shield-lock"></i> Login to Your Account</h2> {% endcomment %}
         <h2 class="gradient-text">
              <i class="bi bi-shield-lock me-2"></i> Login to Your Account
         </h2>
          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
          {% endif %}

          <form method="post" action="{% url 'login' %}">
            {% csrf_token %}

            <div class="input-group mb-3">
              <span class="input-group-text"><i class="bi bi-person"></i></span>
              <input type="text" name="username" class="form-control" placeholder="Username" required>
            </div>

            <div class="input-group mb-4">
              <span class="input-group-text"><i class="bi bi-key"></i></span>
              <input type="password" name="password" class="form-control" placeholder="Password" required>
            </div>

            <div class="d-grid gap-2 animated delay-3">
              <button type="submit" class="btn btn-primary btn-lg" style="color: orange;">
                <i class="bi  me-2" style="color:orange;"></i> Log In
              </button>
            </div>

            <input type="hidden" name="next" value="{% url 'home' %}">
          </form>

          <div class="login-footer animated delay-3">
            <p>© {% now "Y" %} Ethiopian Electric Utility. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
