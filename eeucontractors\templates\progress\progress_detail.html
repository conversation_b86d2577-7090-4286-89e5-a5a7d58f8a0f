{% extends 'base.html' %}
{% load static %}

{% block extra_head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
      <h3>Project Progress Details</h3>
      <div>
        <a href="{% url 'project_progress_update' pk=progress.id %}" class="btn btn-light btn-sm">
          <i class="bi bi-pencil"></i> Edit
        </a>
        <a href="{% url 'workload_list' progress_id=progress.id %}" class="btn btn-light btn-sm">
          <i class="bi bi-list-check"></i> Workloads
        </a>
        <a href="{% url 'project_progress_list' %}" class="btn btn-light btn-sm">
          <i class="bi bi-arrow-left"></i> Back
        </a>
      </div>
    </div>
    
    <div class="card-body">
      <!-- Project Information Section -->
      <div class="row">
        <div class="col-md-6">
          <h4 class="mb-3">Project Information</h4>
          <table class="table table-bordered">
            <tr>
              <th style="width: 40%">Project Name</th>
              <td>{{ progress.project.name }}</td>
            </tr>
            <tr>
              <th>Vendor</th>
              <td>{{ progress.vendor.name }}</td>
            </tr>
            <tr>
              <th>Main Activity</th>
              <td>{{ progress.main_activity.title }}</td>
            </tr>
            <tr>
              <th>Status</th>
              <td>
                <span class="badge {% if progress.status == 'completed' %}bg-success
                      {% elif progress.status == 'in_progress' %}bg-primary
                      {% elif progress.status == 'delayed' %}bg-warning
                      {% else %}bg-secondary{% endif %}">
                  {{ progress.get_status_display }}
                </span>
              </td>
            </tr>
          </table>
        </div>
        
        <div class="col-md-6">
          <h4 class="mb-3">Timeline Information</h4>
          <table class="table table-bordered">
            <tr>
              <th style="width: 40%">Start Date</th>
              <td>{{ progress.start_date|date:"F d, Y" }}</td>
            </tr>
            <tr>
              <th>Expected Completion</th>
              <td>{{ progress.completion_date|date:"F d, Y"|default:"Not specified" }}</td>
            </tr>
            <tr>
              <th>Overall Progress</th>
              <td>
                <div class="progress" style="height: 25px;">
                  <div class="progress-bar bg-success" 
                       role="progressbar" 
                       style="width: {{ progress.progress_percentage }}%;" 
                       aria-valuenow="{{ progress.progress_percentage }}" 
                       aria-valuemin="0" 
                       aria-valuemax="100">
                    {{ progress.progress_percentage }}%
                  </div>
                </div>
              </td>
            </tr>
            <tr>
              <th>Last Updated</th>
              <td>{{ progress.updated_at|date:"F d, Y" }}</td>
            </tr>
          </table>
        </div>
      </div>
      
      <!-- NEW: Time Performance Dashboard -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card bg-light">
            <div class="card-header">
              <h5 class="mb-0">Time Performance Dashboard</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                      <h6 class="mb-0">Schedule Performance</h6>
                    </div>
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Time Efficiency:</span>
                        <span class="badge {% if time_performance.time_efficiency >= 1.0 %}bg-success
                                          {% elif time_performance.time_efficiency >= 0.9 %}bg-info
                                          {% elif time_performance.time_efficiency >= 0.7 %}bg-warning
                                          {% else %}bg-danger{% endif %} p-2">
                          {{ time_performance.time_efficiency|floatformat:2 }}
                        </span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Status:</span>
                        <span class="badge {% if time_performance.status == 'ahead' %}bg-success
                                          {% elif time_performance.status == 'on_track' %}bg-info
                                          {% else %}bg-warning{% endif %} p-2">
                          {{ time_performance.status|title }}
                        </span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Planned Duration:</span>
                        <span>{{ time_performance.planned_duration }} days</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <span>Actual Duration:</span>
                        <span>{{ time_performance.actual_duration }} days</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header bg-primary text-white">
                      <h6 class="mb-0">Progress by Activity</h6>
                    </div>
                    <div class="card-body">
                      {% for activity, data in activity_progress.items %}
                        <div class="mb-2">
                          <div class="d-flex justify-content-between mb-1">
                            <span>{{ activity }}</span>
                            <span>{{ data.percentage }}%</span>
                          </div>
                          <div class="progress" style="height: 20px;">
                            <div class="progress-bar" 
                                 role="progressbar" 
                                 style="width: {{ data.percentage }}%;" 
                                 aria-valuenow="{{ data.percentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- NEW: Progress Timeline Chart -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header bg-success text-white">
              <h5 class="mb-0">Progress Timeline</h5>
            </div>
            <div class="card-body">
              <canvas id="progressChart" height="100"></canvas>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Existing Overall Project Progress Summary -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card bg-light">
            <div class="card-header">
              <h5 class="mb-0">Overall Project Progress Summary</h5>
            </div>
            <div class="card-body">
              <div class="progress" style="height: 30px;">
                <div class="progress-bar bg-success" 
                     role="progressbar" 
                     style="width: {{ progress.progress_percentage }}%;" 
                     aria-valuenow="{{ progress.progress_percentage }}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                  {{ progress.progress_percentage }}%
                </div>
              </div>
              <div class="mt-3">
                <p><strong>Status:</strong> {{ progress.get_status_display }}</p>
                <p><strong>Last Calculated:</strong> {{ progress.updated_at|date:"F d, Y H:i" }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Workloads and Progress Section -->
      <div class="row mt-4">
        <div class="col-12">
          <h4 class="mb-3">Workloads and Progress</h4>
          
          {% if workloads %}
            <div class="table-responsive">
              <table class="table table-striped">
                <thead class="table-dark">
                  <tr>
                    <th>Sub Activity</th>
                    <th>Quantity</th>
                    <th>Expected Duration</th>
                    <th>Progress</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for workload in workloads %}
                    <tr>
                      <td>{{ workload.sub_activity.name }}</td>
                      <td>{{ workload.quantity }} units</td>
                      <td>{{ workload.expected_duration }} days</td>
                      <td>
                        {% with latest_progress=workload.latest_progress %}
                          {% if latest_progress %}
                            <div class="progress" style="height: 20px;">
                              <div class="progress-bar bg-success" 
                                   role="progressbar" 
                                   style="width: {{ latest_progress.progress }}%;" 
                                   aria-valuenow="{{ latest_progress.progress }}" 
                                   aria-valuemin="0" 
                                   aria-valuemax="100">
                                {{ latest_progress.progress }}%
                              </div>
                            </div>
                          {% else %}
                            <span class="badge bg-secondary">No progress recorded</span>
                          {% endif %}
                        {% endwith %}
                      </td>
                      <td>
                        <a href="{% url 'workload_detail' workload_id=workload.id %}" class="btn btn-sm btn-info">
                          <i class="bi bi-eye"></i> View
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="alert alert-info">
              No workloads have been added yet. 
              <a href="{% url 'add_workload' progress_id=progress.id %}" class="alert-link">Add a workload</a> to start tracking progress.
            </div>
          {% endif %}
          
          <div class="mt-3">
            <a href="{% url 'add_workload' progress_id=progress.id %}" class="btn btn-primary">
              <i class="bi bi-plus-circle"></i> Add New Workload
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Chart -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Timeline data from backend
    const timelineData = {{ timeline_data|safe }};
    
    if (timelineData && timelineData.length > 0) {
      const ctx = document.getElementById('progressChart').getContext('2d');
      
      // Extract dates and progress values
      const dates = timelineData.map(item => item.date);
      const progressValues = timelineData.map(item => item.progress);
      
      // Create the chart
      const progressChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: dates,
          datasets: [{
            label: 'Progress %',
            data: progressValues,
            backgroundColor: 'rgba(40, 167, 69, 0.2)',
            borderColor: 'rgba(40, 167, 69, 1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: 'Progress %'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Date'
              }
            }
          },
          plugins: {
            title: {
              display: true,
              text: 'Project Progress Over Time'
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `Progress: ${context.parsed.y}%`;
                }
              }
            }
          }
        }
      });
    }
  });
</script>
{% endblock %}
