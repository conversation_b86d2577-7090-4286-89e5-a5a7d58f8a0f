from django.urls import path
from django.contrib.auth.views import LogoutView
from . import views
from django.contrib.auth import views as auth_views
from django.contrib.auth.views import LoginView

urlpatterns = [
    # Keep all existing URL patterns
    path('', views.login_view, name='login'),
    path('home/', views.home, name='home'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard_view, name='dashboard'),
    # Vendor Registration
    path('vendors/register/', views.vendors_registration_view, name='vendors_registration'),
    path('contractors/registered/', views.registered_contractors_list, name='registered_contractors'),
    path('vendors/list/', views.vendors_list_view, name='vendors_list'),
    path('vendors/registered/', views.registered_contractors_list, name='registered_contractors_list'),
    path('vendors/enterprises/', views.new_enterprises_list, name='new_enterprises_list'),
    path('vendors/<int:vendor_id>/', views.vendor_detail_view, name='vendor_detail'),
    path('create/', views.vendor_create_view, name='vendor_create'),
    path('list/', views.vendor_list_view, name='vendor_information_list'),

    # Projects - use the existing function-based view
    path('projects/', views.Project_List.as_view(), name='project_list'), 
    path('projects/create/', views.project_create, name='project_create'),
    path('projects/<int:pk>/edit/', views.project_edit, name='project_edit'),
    
    # Project Progress
    path('progress/', views.project_progress_list, name='project_progress_list'),
    path('progress/create/', views.create_project_progress, name='create_project_progress'),
    path('progress/<int:pk>/edit/', views.project_progress_update, name='project_progress_update'),
    path('progress/<int:pk>/delete/', views.project_progress_delete, name='project_progress_delete'),
    path('progress/<int:pk>/detail/', views.project_progress_detail, name='project_progress_detail'),
    
   path('workloads/detail/<int:workload_id>/', views.workload_detail, name='workload_detail'),
    #  # Workload List (by ProjectProgress)
    path('workloads/<int:progress_id>/', views.SubActivityWorkloadListView.as_view(), name='workload_list'),
    # Add new workload for a ProjectProgress
    path('workloads/<int:progress_id>/add/', views.add_workload, name='add_workload'),
    # Edit workload
    path('workloads/edit/<int:workload_id>/', views.edit_workload, name='edit_workload'),
    # Delete workload
    path('workloads/delete/<int:workload_id>/', views.delete_workload, name='delete_workload'),
    # Workload detail with progress records formset
    path('workloads/detail/<int:workload_id>/', views.workload_detail, name='workload_detail'),
    # views.py
    path('progress/select/<int:progress_id>/', views.select_project_progress, name='select_project_progress'),

    # Vendor History
    path('vendor-history/', views.VendorHistoryListView.as_view(), name='vendor_history_list'),
    path('vendor/<int:vendor_id>/history/', views.VendorHistoryListView.as_view(), name='vendor_history_list'),
    path('vendor-history/add/', views.add_vendor_history, name='add_vendor_history'),
    path('vendor/<int:vendor_id>/history/add/', views.add_vendor_history, name='add_vendor_history'), 
    path('project/<int:project_id>/history/add/', views.add_vendor_history, name='add_project_history'), 
    path('vendor-history/<int:history_id>/', views.vendor_history_detail, name='vendor_history_detail'),
    path('vendor-history/<int:history_id>/edit/', views.edit_vendor_history, name='edit_vendor_history'),
    
       #Documents 
    path('vendor-documents/', views.VendorDocumentListView.as_view(), name='all_vendor_documents'),
    path('vendor/<int:vendor_id>/documents/', views.VendorDocumentListView.as_view(), name='vendor_document_list'),
    path('documents/add/', views.add_vendor_document, name='add_vendor_document'),
    path('documents/<int:document_id>/edit/', views.edit_vendor_document, name='edit_vendor_document'),
    path('documents/<int:document_id>/delete/', views.delete_vendor_document, name='delete_vendor_document'),
    

    # Reports - New URLs
    path('reports/vendor-performance/', views.vendor_performance_report, name='vendor_performance_report'),
    path('reports/project-status/', views.project_status_report, name='project_status_report'),
    path('reports/progress-summary/', views.progress_summary_report, name='progress_summary_report'),
    
    #User Profile - New URLs
   # path('profile/', views.user_profile, name='user_profile'),
    path('profile/settings/', views.user_settings, name='user_settings'),
    
   


    # API Endpoints - New URLs
    path('api/project/<int:project_id>/vendors/', views.project_vendors_api, name='project_vendors_api'),
    

]
