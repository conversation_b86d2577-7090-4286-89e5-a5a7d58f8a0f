{% extends "base.html" %}
{% load static %}
{% block title %}{{ title }}{% endblock %}

{% block content %}
<style>
  /* Make Description textarea smaller width */
  textarea#id_description {
    max-width: 200px;  /* limit width */
    height: 80px;  /* reasonable height */
    resize: vertical;  /* allow vertical resize */
  }

  /* Style the card border */
  .card {
    border: 1px solid #ddd; /* light gray border */
    transition: border-color 0.3s ease;
  }

  /* Input, select, textarea default style */
  input.form-control, select.form-select, textarea.form-control {
    border: 1px solid #ccc;
    transition: border-color 0.3s ease;
  }

  /* Hover and focus effect for inputs */
  input.form-control:hover, select.form-select:hover, textarea.form-control:hover,
  input.form-control:focus, select.form-select:focus, textarea.form-control:focus {
    border-color: orange;
    box-shadow: 0 0 5px rgba(255, 165, 0, 0.5);
    outline: none;
  }
</style>

<div class="container mt-4" style="max-width: 900px;">
  <div class="card shadow-sm rounded-3 p-4">
    <h3 class="mb-4">{{ title }}</h3>

    <form method="post" enctype="multipart/form-data" novalidate>
      {% csrf_token %}

      <div class="row">
        <!-- Vendor Dropdown -->
        <div class="col-md-6 mb-3">
          <label for="id_vendor" class="form-label fw-semibold">Vendor:</label>
          {{ form.vendor }}
        </div>

        <!-- Auto-filled Vendor ID Number -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">ID Number:</label>
          {{ form.vendor_id_number }}
        </div>

        <!-- Auto-filled Vendor Phone -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">Phone:</label>
          {{ form.vendor_phone }}
        </div>

        <!-- Auto-filled Vendor Level -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Level:</label>
          {{ form.vendor_level }}
        </div>

        <!-- Auto-filled Vendor Type -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">&nbsp;&nbsp;Type:</label>
          {{ form.vendor_type }}
        </div>
      </div>

      <div class="row">
        <!-- Render all other fields dynamically, excluding vendor and auto-filled fields -->
        {% for field in form %}
          {% if field.name != 'vendor' and field.name != 'vendor_id_number' and field.name != 'vendor_phone' and field.name != 'vendor_level' and field.name != 'vendor_type' %}
            <div class="col-md-6 mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-semibold">{{ field.label }}</label>
              {{ field }}
              {% for error in field.errors %}
                <div class="text-danger small mt-1">{{ error }}</div>
              {% endfor %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <!-- Form buttons: Cancel and Save -->
      <div class="d-flex justify-content-between mt-4">
        <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Cancel
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Select vendor dropdown
  const vendorSelect = document.getElementById('id_vendor');

  // Vendor data to auto-fill fields when vendor is selected
  const vendorData = {
    {% for v in vendors %}
      "{{ v.id }}": {
        id_number: "{{ v.identification_number }}",
        phone: "{{ v.phone_number }}",
        level: "{{ v.level }}",
        type: "{{ v.type }}"
      },
    {% endfor %}
  };

  // On vendor change, fill auto fields with vendor info
  vendorSelect.addEventListener('change', function () {
    const selected = this.value;
    const data = vendorData[selected];
    if (data) {
      document.getElementById('id_vendor_id_number').value = data.id_number;
      document.getElementById('id_vendor_phone').value = data.phone;
      document.getElementById('id_vendor_level').value = data.level;
      document.getElementById('id_vendor_type').value = data.type;
    }
  });
</script>
{% endblock %}






{% comment %} {% extends "base.html" %}
{% load static %}
{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4" style="max-width: 900px;">
  <div class="card shadow-sm rounded-3 p-4">
    <h3 class="mb-4">{{ title }}</h3>

    <form method="post" enctype="multipart/form-data" novalidate>
      {% csrf_token %}

      <div class="row">
        <!-- Vendor Dropdown -->
        <div class="col-md-6 mb-3">
          <label for="id_vendor" class="form-label fw-semibold">Vendor</label>
          {{ form.vendor }}
        </div>

        <!-- Auto-filled Vendor ID Number -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">ID Number</label>
          {{ form.vendor_id_number }}
        </div>

        <!-- Auto-filled Vendor Phone -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">Phone</label>
          {{ form.vendor_phone }}
        </div>

        <!-- Auto-filled Vendor Level -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">Level</label>
          {{ form.vendor_level }}
        </div>

        <!-- Auto-filled Vendor Type -->
        <div class="col-md-6 mb-3">
          <label class="form-label fw-semibold">Type</label>
          {{ form.vendor_type }}
        </div>
      </div>

      <div class="row">
        <!-- Render all other fields dynamically, excluding vendor and auto-filled fields -->
        {% for field in form %}
          {% if field.name != 'vendor' and field.name != 'vendor_id_number' and field.name != 'vendor_phone' and field.name != 'vendor_level' and field.name != 'vendor_type' %}
            <div class="col-md-6 mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-semibold">{{ field.label }}</label>
              {{ field }}
              {% for error in field.errors %}
                <div class="text-danger small mt-1">{{ error }}</div>
              {% endfor %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <!-- Form buttons: Cancel and Save -->
      <div class="d-flex justify-content-between mt-4">
        <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Cancel
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Select vendor dropdown
  const vendorSelect = document.getElementById('id_vendor');

  // Vendor data to auto-fill fields when vendor is selected
  const vendorData = {
    {% for v in vendors %}
      "{{ v.id }}": {
        id_number: "{{ v.identification_number }}",
        phone: "{{ v.phone_number }}",
        level: "{{ v.level }}",
        type: "{{ v.type }}"
      },
    {% endfor %}
  };

  // On vendor change, fill auto fields with vendor info
  vendorSelect.addEventListener('change', function () {
    const selected = this.value;
    const data = vendorData[selected];
    if (data) {
      document.getElementById('id_vendor_id_number').value = data.id_number;
      document.getElementById('id_vendor_phone').value = data.phone;
      document.getElementById('id_vendor_level').value = data.level;
      document.getElementById('id_vendor_type').value = data.type;
    }
  });
</script>
{% endblock %} {% endcomment %}
