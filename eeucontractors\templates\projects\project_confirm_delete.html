{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Project: {{ project.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-6">
      <div class="card border-danger">
        <div class="card-header bg-danger text-white">
          <h4 class="mb-0">
            <i class="bi bi-exclamation-triangle me-2"></i>Delete Project
          </h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-circle me-2"></i>
            <strong>Warning:</strong> This action cannot be undone.
          </div>
          
          <p class="mb-4">Are you sure you want to delete the project <strong>"{{ project.name }}"</strong>?</p>
          
          <!-- Project Summary -->
          <div class="mb-4">
            <h5 class="border-bottom pb-2">Project Details</h5>
            <div class="row">
              <div class="col-md-6">
                <p><strong>Project Name:</strong> {{ project.name }}</p>
                <p><strong>Region:</strong> {{ project.region }}</p>
                <p><strong>Type:</strong> {{ project.get_type_display }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Vendor:</strong> {{ project.vendor.name }}</p>
                <p><strong>Status:</strong> {{ project.get_status_display }}</p>
                <p><strong>Approved:</strong> {% if project.approved %}Yes{% else %}No{% endif %}</p>
              </div>
            </div>
          </div>
          
          <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between mt-4">
              <a href="{% url 'project_detail' project.pk %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Cancel
              </a>
              <button type="submit" class="btn btn-danger">
                <i class="bi bi-trash me-1"></i>Delete Project
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}