{% extends 'base.html' %}
{% block content %}

<div class="container mt-3">
  <div class="card shadow-sm rounded-4 p-4">
    <h2 class="mb-3 text-primary">Add Project Progress</h2>

    <form method="post">
      {% csrf_token %}
      
      <div class="row">
        {% for field in form %}
          {% if field.name != 'main_activity' %}
            <div class="col-md-6 mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold">{{ field.label }}</label>
              {{ field }}
              {% if field.errors %}
                <div class="text-danger small">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% else %}
            <div class="col-12 mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold">{{ field.label }}</label>
              {{ field }}
              {% if field.errors %}
                <div class="text-danger small">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <div class="card mt-3 mb-4">
        <div class="card-header bg-light">
          <h5 class="mb-0">Sub Activities</h5>
        </div>
        <div class="card-body" id="subactivity-formset">
          {{ formset.management_form }}
          {% for form in formset %}
          <div class="row mb-2 align-items-center">
            <div class="col-md-3">
              {{ form.sub_activity }}
            </div>
            <div class="col-md-3">
              {{ form.progress }}
            </div>
            <div class="col-md-3">
              {{ form.start_date }}
            </div>
            <div class="col-md-3">
              {{ form.end_date }}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>

      <div class="d-flex justify-content-between">
        <a href="{% url 'progress_list' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Back
        </a>
        <button type="submit" class="btn btn-success">
          <i class="bi bi-save"></i> Submit
        </button>
      </div>
    </form>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script type="text/javascript">
  $(document).ready(function () {
    // Add Bootstrap classes to form elements
    $('input, select, textarea').addClass('form-control');
    
    $("#id_main_activity").change(function () {
      let mainActivityId = $(this).val();

      $.ajax({
        url: "{% url 'load_subactivities' %}",
        data: { main_activity: mainActivityId },
        success: function (data) {
          let subactivities = data.subactivities;

          let html = "";
          subactivities.forEach(function (sub) {
            html += `
              <div class="row mb-2 align-items-center">
                <div class="col-md-3">
                  <input type="hidden" name="subactivityprogress_set-0-sub_activity" value="${sub.id}">
                  <label class="form-label fw-bold">${sub.name}</label>
                </div>
                <div class="col-md-3">
                  <input type="number" name="subactivityprogress_set-0-progress" class="form-control" placeholder="Progress %">
                </div>
                <div class="col-md-3">
                  <input type="date" name="subactivityprogress_set-0-start_date" class="form-control" placeholder="Start Date">
                </div>
                <div class="col-md-3">
                  <input type="date" name="subactivityprogress_set-0-end_date" class="form-control" placeholder="End Date">
                </div>
              </div>
            `;
          });

          $("#subactivity-formset").html(html);
        },
      });
    });
  });
</script>

{% endblock %}
