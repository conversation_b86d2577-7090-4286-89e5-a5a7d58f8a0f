{% extends 'base.html' %} {% load static %} {% block extra_css %}
<style>
  .detail-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .detail-card-header {
    background-color: #79aec8;
    color: white;
    padding: 10px 15px;
    border-radius: 8px 8px 0 0;
    font-weight: 500;
  }

  .detail-card-body {
    padding: 15px;
  }

  .detail-row {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
  }

  .detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .detail-label {
    flex: 0 0 40%;
    font-weight: 500;
    color: #555;
  }

  .detail-value {
    flex: 0 0 60%;
  }

  .back-button {
    margin-bottom: 20px;
  }

  .yes-badge {
    background-color: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .no-badge {
    background-color: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
</style>
{% endblock %} {% block content %}
<div class="container py-4">
  <div class="back-button">
    <a
      href="{% url 'registered_contractors_list' %}"
      class="btn btn-outline-primary"
    >
      <i class="bi bi-arrow-left"></i> Back to Registered Contractors
    </a>
  </div>

  <h2 class="mb-4">{{ contractor.contractor_name }}</h2>

  <!-- Basic Information -->
  <div class="detail-card">
    <div class="detail-card-header">
      <i class="bi bi-info-circle me-2"></i> Basic Information
    </div>
    <div class="detail-card-body">
      <div class="detail-row">
        <div class="detail-label">የተመዘገበው ስም:</div>
        <div class="detail-value">{{ contractor.contractor_name }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የኮንትራክተሩ አይነት:</div>
        <div class="detail-value">
          {% if contractor.contractor_type == 'contractor' %} Contractor {% elif
          contractor.contractor_type == 'enterprise' %} Enterprise {% else %}
          New Enterprise {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የመወዳደሪያ መለያ ቁጥር:</div>
        <div class="detail-value">{{ contractor.identification_number }}</div>
      </div>
    </div>
  </div>

  <!-- Human Resources -->
  <div class="detail-card">
    <div class="detail-card-header">
      <i class="bi bi-people-fill me-2"></i> Human Resources
    </div>
    <div class="detail-card-body">
      <div class="detail-row">
        <div class="detail-label">የሙያ ብቃት አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_professional_license %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የኤሌክትሪክ ስራ ተቋራጭ ደረጃ:</div>
        <div class="detail-value">{{ contractor.electric_grade }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የሰው ኃይል አሟልቷል?</div>
        <div class="detail-value">
          {% if contractor.has_human_resource %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የኤሌክትሪካል ምህንድስና:</div>
        <div class="detail-value">{{ contractor.electrical_engineers }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የኤሌክትሮ መካኒካል ምህንድስና:</div>
        <div class="detail-value">
          {{ contractor.electromechanical_engineers }}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የሲቪል ምህንድስና:</div>
        <div class="detail-value">{{ contractor.civil_engineers }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">ኤሌክትሪሺያን/ፎርማን:</div>
        <div class="detail-value">{{ contractor.electricians_foremen }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">ሰርቬየር:</div>
        <div class="detail-value">{{ contractor.surveyors }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የመስመር ሰራተኞች:</div>
        <div class="detail-value">{{ contractor.line_workers }}</div>
      </div>
    </div>
  </div>

  <!-- Document Checklist -->
  <div class="detail-card">
    <div class="detail-card-header">
      <i class="bi bi-file-earmark-check-fill me-2"></i> Document Checklist
    </div>
    <div class="detail-card-body">
      <div class="detail-row">
        <div class="detail-label">የሥራ ልምድ ማስረጃ አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_work_experience_doc %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የንግድ ፈቃድ አለው?</div>
        <div class="detail-value">
          {% if contractor.has_trade_license %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የኤሌክትሪክ ደረጃ የምስክር ወረቀት አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_energy_certification %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የሰው ኃይልና መሳሪያ አሟልቷል?</div>
        <div class="detail-value">
          {% if contractor.has_equipment_resources %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">VAT እና የግብር የምስክር ወረቀት አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_vat_and_tax %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የመልካም ስራ አፈጻጸም አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_good_performance %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የፋይናንስ አቅም መረጃ አቅርቧል?</div>
        <div class="detail-value">
          {% if contractor.has_financial_capacity %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Registration Details -->
  <div class="detail-card">
    <div class="detail-card-header">
      <i class="bi bi-clipboard-check-fill me-2"></i> Registration Details
    </div>
    <div class="detail-card-body">
      <div class="detail-row">
        <div class="detail-label">ተመዝግቧል?</div>
        <div class="detail-value">
          {% if contractor.is_registered %}
          <span class="yes-badge">Yes</span>
          {% else %}
          <span class="no-badge">No</span>
          {% endif %}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የመዝጋቢ ሰራተኛው ስም:</div>
        <div class="detail-value">{{ contractor.registrar_name }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">መለያ ቁጥር:</div>
        <div class="detail-value">{{ contractor.registrar_id }}</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የምዝገባ ቀን:</div>
        <div class="detail-value">
          {{ contractor.created_at|date:"F d, Y"|default:"N/A" }}
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-label">የምዝገባ ሰዓት:</div>
        <div class="detail-value">
          {{ contractor.created_at|time:"g:i a"|default:"N/A" }}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
