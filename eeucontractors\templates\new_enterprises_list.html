{% extends 'base.html' %} {% load static %} {% block extra_css %}
<style>
  /* Admin-like styling */
  body {
    font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  }

  .module {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .module h2 {
    background: #79aec8;
    color: #fff;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
    padding: 8px 10px;
    border-radius: 4px 4px 0 0;
  }

  .changelist {
    width: 100%;
  }

  .changelist-actions {
    padding: 10px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
  }

  #changelist-form {
    overflow-x: auto;
  }

  #changelist table {
    width: 100%;
    border-collapse: collapse;
  }

  #changelist table thead th {
    padding: 8px;
    text-align: left;
    font-size: 13px;
    font-weight: 600;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    color: #666;
  }

  #changelist table tbody tr {
    border-bottom: 1px solid #eee;
  }

  #changelist table tbody tr:hover {
    background-color: #f5f5f5;
  }

  #changelist table tbody td {
    padding: 8px;
    font-size: 13px;
    vertical-align: middle;
  }

  .paginator {
    padding: 10px;
    background: #f8f8f8;
    border-top: 1px solid #eee;
    text-align: right;
    color: #666;
    font-size: 13px;
  }

  .object-tools {
    float: right;
    margin-top: -32px;
    margin-right: 10px;
  }

  .object-tools a {
    display: inline-block;
    background: #79aec8;
    color: #fff;
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 4px;
    text-decoration: none;
  }

  .object-tools a:hover {
    background: #609ab6;
  }

  .addlink {
    padding-left: 16px;
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMFY4TTAgOEg0TTggOFYxNk04IDhIOCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=)
      0 0 no-repeat;
  }
</style>
{% endblock %} {% block content %}
<div id="content" class="container py-4">
  <div id="content-main">
    <div class="module" id="changelist">
      <h2>
        {% if contractor_type == 'contractor' %} New Contractors {% elif
        contractor_type == 'enterprise' %} New Enterprises {% elif
        contractor_type == 'new_enterprise' %} New Enterprise Checklists {% else
        %} All New Entities {% endif %}
        <div class="object-tools">
          <a href="{% url 'vendors_registration' %}" class="addlink">Add</a>
        </div>
      </h2>

      <div class="changelist-actions">
        <span class="small">
          {{ total_count }} {% if contractor_type == 'contractor' %} new
          contractors {% elif contractor_type == 'enterprise' %} new enterprises
          {% elif contractor_type == 'new_enterprise' %} new enterprise
          checklists {% else %} new entities {% endif %}
        </span>

        <div class="filter-options mt-2">
          <div class="btn-group" role="group">
            <a
              href="{% url 'new_enterprises_list' %}"
              class="btn btn-sm {% if contractor_type == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >All</a
            >
            <a
              href="{% url 'new_enterprises_list' %}?contractor_type=contractor"
              class="btn btn-sm {% if contractor_type == 'contractor' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >Contractors</a
            >
            <a
              href="{% url 'new_enterprises_list' %}?contractor_type=enterprise"
              class="btn btn-sm {% if contractor_type == 'enterprise' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >Enterprises</a
            >
            <a
              href="{% url 'new_enterprises_list' %}?contractor_type=new_enterprise"
              class="btn btn-sm {% if contractor_type == 'new_enterprise' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >New Enterprise Checklists</a
            >
          </div>
        </div>
      </div>

      <form id="changelist-form">
        <div class="results">
          <table id="result_list">
            <thead>
              <tr>
                <th scope="col">የተመዘገበው ስም</th>
                <th scope="col">የኮንትራክተሩ አይነት</th>
                <th scope="col">የመወዳደሪያ መለያ ቁጥር</th>
                <th scope="col">ተመዝግቧል?</th>
                <th scope="col">የመዝጋቢ ሰራተኛው ስም</th>
                <th scope="col">Created at</th>
              </tr>
            </thead>
            <tbody>
              {% for enterprise in new_enterprises %}
              <tr class="{% cycle 'row1' 'row2' %}">
                <td>
                  <a
                    href="{% url 'contractor_detail' enterprise.id %}"
                    class="link-primary"
                  >
                    {{ enterprise.contractor_name }}
                  </a>
                </td>
                <td>New Enterprise</td>
                <td>{{ enterprise.identification_number }}</td>
                <td>{{ enterprise.is_registered|yesno:"True,False" }}</td>
                <td>{{ enterprise.registrar_name }}</td>
                <td>{{ enterprise.created_at|date:"M d, Y, g:i a" }}</td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="6" class="text-center py-4">
                  <p class="text-muted">No new enterprises found.</p>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <div class="paginator">{{ total_count }} new enterprises</div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
