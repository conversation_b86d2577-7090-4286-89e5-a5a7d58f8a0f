{% extends "base.html" %} {% load static %} {% block content %}
<style>
  /* Overall background */

 body {
  background-color: #faf7f0; /* soft warm beige, easy on eyes, no green/orange overload */
}
.card {
  background-color: #fffef8; /* very light warm cream with subtle orange tint */
  border: 1px solid #f68b1e33; /* transparent orange border */
}

.card-body {
  background-color: #f3fff7; /* very light minty green, subtle and modern */
  padding: 1.5rem; /* ensure some breathing space */
  border-radius: 0 0 8px 8px; /* round bottom corners gently */
}

.table {
  background-color: #ffffff; /* pure white for table for clear readability */
}

.table-hover tbody tr:hover {
  background-color: rgba(246, 139, 30, 0.12); /* gentle orange highlight on hover */
  transition: background-color 0.3s ease;
}
/* Input focus - themed with orange */
.form-control:focus {
  border-color: #f68b1e;
  box-shadow: 0 0 0 3px rgba(246, 139, 30, 0.2);
  background-color: #fff;
}

/* Select arrow color */
select.form-control {
  background-image: linear-gradient(45deg, transparent 50%, #f68b1e 50%),
    linear-gradient(135deg, #f68b1e 50%, transparent 50%);
}

/* Checkbox checked */
.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

/* Form header */
.form-header {
  background: linear-gradient(135deg, #f68b1e, #28a745);
  color: white;
}

/* Fieldset titles */
.fieldset-title {
  background: linear-gradient(90deg, #fff7f0, #f0fdf4);
  color: #333;
}

/* Success message */
.success-message {
  background-color: #dff0d8;
  color: #2e7d32;
}

/* Submit button */
.btn-submit {
  background: linear-gradient(135deg, #28a745, #f68b1e);
  color: white;
}

.btn-submit:hover {
  background: linear-gradient(135deg, #218838, #e5760d);
}

/* Input hover */
.professional-count-item input:hover {
  border-color: #f68b1e;
}

/* Input focus for professional count */
.professional-count-item input:focus {
  border-color: #f68b1e;
  box-shadow: 0 0 0 3px rgba(246, 139, 30, 0.15);
}

  /* Add spacing between specific fields */
  .electric-grade-field {
    margin-right: 20px; /* Add right margin to create space */
  }

  /* Adjust spacing for human resource field */
  .human-resource-field {
    margin-left: 20px; /* Add left margin to create space */
  }

  /* Improve alignment of inline field groups */
  .inline-field-group {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .inline-field-group label {
    margin-bottom: 0;
    margin-right: 10px;
    white-space: nowrap;
    flex-shrink: 0;
  }
  /* Make the form more compact */
  .form-group {
    margin-bottom: 0.3rem; /* Reduced from 0.5rem */
  }

  /* Modern, attractive input styling */
  .form-control {
    padding: 0.2rem 0.4rem;
    height: calc(1.4em + 0.4rem + 2px);
    font-size: 0.875rem;
    border: 1px solid #dde2e6;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    width: auto;
    min-width: 120px;
    max-width: 180px;
    background-color: #f8f9fa;
  }

  .form-control:focus {
    border-color: #4ca1af;
    box-shadow: 0 0 0 3px rgba(76, 161, 175, 0.15);
    background-color: #fff;
    outline: none;
  }

  /* Adjust select fields with same styling */
  select.form-control {
    width: auto;
    min-width: 120px;
    max-width: 180px;
    background-image: linear-gradient(45deg, transparent 50%, #6c757d 50%),
      linear-gradient(135deg, #6c757d 50%, transparent 50%);
    background-position: calc(100% - 15px) calc(1em - 2px),
      calc(100% - 10px) calc(1em - 2px);
    background-size: 5px 5px, 5px 5px;
    background-repeat: no-repeat;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding-right: 25px;
  }

  /* Make number inputs narrower but stylish */
  input[type="number"] {
    text-align: center;
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    background-color: #f8f9fa;
  }

  /* Checkbox styling */
  .form-check-input {
    margin-top: 0.2rem;
    border: 1px solid #dde2e6;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .form-check-input:checked {
    background-color: #4ca1af;
    border-color: #4ca1af;
  }

  /* Fieldset styling to match modern inputs */
  .fieldset {
    margin-bottom: 0.5rem;
    border: 1px solid #dde2e6;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  .fieldset-title {
    padding: 0.3rem 0.5rem;
    font-size: 0.85rem;
    background: linear-gradient(135deg, #f8f9fa, #edf2f7);
    border-bottom: 1px solid #dde2e6;
    color: #2c3e50;
    font-weight: 600;
  }

  /* Submit button styling to match */
  .btn-submit {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
  }

  .btn-submit:hover {
    background: linear-gradient(135deg, #218838, #1aa179);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .fieldset-body {
    padding: 0.3rem 0.5rem; /* Reduced padding */
  }

  .fieldset-title {
    padding: 0.3rem 0.5rem; /* Reduced padding */
    font-size: 0.85rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #e0e0e0;
    color: #495057;
    font-weight: 600;
  }

  /* Style for electric grade radio buttons */
  .electric-grade-options {
    display: flex;
    flex-wrap: wrap;
  }

  .electric-grade-options .form-check {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
  }

  .form-check-label {
    font-size: 0.85rem;
  }

  /* Make form controls smaller and more compact */
  .form-control {
    padding: 0.2rem 0.4rem; /* Reduced padding */
    height: calc(1.4em + 0.4rem + 2px); /* Reduced height */
    font-size: 0.875rem;
    border-color: #ced4da;
    width: auto;
    min-width: 120px;
    max-width: 180px;
  }

  .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  /* Adjust select fields */
  select.form-control {
    width: auto;
    min-width: 120px;
    max-width: 180px;
  }

  /* Make number inputs narrower but not too short */
  input[type="number"] {
    text-align: center;
    width: 80px;
    min-width: 80px;
    max-width: 80px;
  }

  /* Ensure labels don't wrap unnecessarily */
  .form-group label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    font-size: 0.8rem;
  }

  /* Reduce spacing in the submit row */
  .submit-row {
    padding: 0.3rem 0; /* Reduced from 0.5rem */
    text-align: right;
  }

  .btn-submit {
    background-color: #28a745;
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    border: none;
    transition: background-color 0.15s ease-in-out;
  }

  .btn-submit:hover {
    background-color: #218838;
  }

  /* Success message styling */
  .success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
  }

  .success-message i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
  }

  /* Form card styling - reduce overall width */
  .form-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 850px; /* Reduce overall form width */
    margin: 0 auto;
  }

  .form-header {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
  }

  .form-logo {
    height: 25px;
    margin-right: 10px;
  }

  /* Number input styling */
  input[type="number"] {
    text-align: center;
  }

  /* Section divider */
  .section-divider {
    margin: 0.3rem 0; /* Reduced from 0.5rem */
    border-top: 1px dashed #e0e0e0;
  }

  /* Checkbox styling */
  .form-check-input {
    margin-top: 0.2rem;
  }

  /* Improved form row layout with reduced spacing */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -3px; /* Reduced from -5px */
    margin-left: -3px; /* Reduced from -5px */
    margin-bottom: 0.2rem; /* Added to reduce space between rows */
  }

  .form-group-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding-right: 3px; /* Reduced from 5px */
    padding-left: 3px; /* Reduced from 5px */
  }

  .form-group-col-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 3px; /* Reduced from 5px */
    padding-left: 3px; /* Reduced from 5px */
  }

  .form-group-col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 3px; /* Reduced from 5px */
    padding-left: 3px; /* Reduced from 5px */
  }

  /* Checkbox alignment */
  .form-check {
    display: flex;
    align-items: flex-start;
    padding-left: 1.5rem;
  }

  .form-check-input {
    margin-left: -1.5rem;
    margin-top: 0.3rem;
  }

  /* Reduce spacing in section titles */
  h6.mb-2 {
    margin-bottom: 0.3rem !important; /* Reduced from default */
  }

  /* Inline label-input styling for electric grade field */
  .inline-field-group {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .inline-field-group label {
    margin-bottom: 0;
    margin-right: 10px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .inline-field-group label::before {
    display: none; /* Remove the vertical accent line for inline labels */
  }

  .inline-field-group .form-control {
    flex-grow: 1;
  }

  /* Inline label-input styling with input before label */
  .inline-field-reverse {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-direction: row-reverse; /* Reverse the order to put input first */
    justify-content: flex-end; /* Align to the end (right in LTR) */
  }

  .inline-field-reverse label {
    margin-bottom: 0;
    margin-left: 10px; /* Margin on left instead of right */
    margin-right: 0;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .inline-field-reverse label::before {
    display: none; /* Remove the vertical accent line */
  }

  .inline-field-reverse .form-control {
    flex-grow: 0;
    width: 150px; /* Fixed width for these inputs */
    min-width: 150px;
    max-width: 150px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .form-group-col-4,
    .form-group-col-6 {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .form-control,
    select.form-control {
      width: 100%;
      max-width: 100%;
    }

    input[type="number"] {
      max-width: 100%;
      width: 100%;
    }
  }

  /* Improved spacing for registration section with narrower gaps */
  .registration-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 0.5rem;
    width: 100%;
    gap: 15px;
    margin-left: 20px; /* Add left margin to the entire row */
  }

  /* Registration checkbox styling */
  .registration-check {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
  }

  .registration-check .form-check {
    margin-bottom: 0;
    padding-left: 1.5rem;
  }

  /* Registrar input fields styling - label first, then input */
  .registrar-field {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    margin-left: 40px; /* Increased left margin to 40px */
  }

  .registrar-field label {
    margin-bottom: 0;
    white-space: nowrap;
    margin-right: 8px;
    font-size: 0.8rem;
  }

  .registrar-field .form-control {
    width: 150px;
    min-width: 150px;
    max-width: 150px;
  }

  /* Additional margin for the third element */
  .registrar-field:nth-child(3) {
    margin-left: 60px; /* Even more margin for the third element */
  }

  /* Add margin to document checklist checkboxes */
  .document-checklist .form-check {
    margin-left: 20px; /* Add left margin to all checkboxes in document checklist */
  }

  /* Ensure proper alignment of checkbox and label */
  .document-checklist .form-check-label {
    margin-left: 5px; /* Add a little space between checkbox and label */
  }

  /* Add margin to professional count fields only (not the heading) */
  .professional-count-section .form-group:not(:first-child) {
    margin-left: 20px; /* Add left margin to all fields except the heading */
  }

  /* Remove margin from the heading */
  .professional-count-heading {
    margin-left: 0; /* No margin for the heading */
  }

  /* Inline styling for professional count fields */
  .professional-count-inline {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .professional-count-inline label {
    margin-bottom: 0;
    margin-right: 10px;
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 0.8rem;
  }

  .professional-count-inline input {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
  }

  /* Enhanced professional count section styling */
  .professional-count-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    
    column-gap: 15px; /* Horizontal space only */
    row-gap: 0px;     /* ✅ Remove vertical space between rows in each column */

    margin-left: 20px;
    margin-top: 10px;
  }

  /* Enhanced individual professional count item styling */
  .professional-count-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .professional-count-item label {
    margin-bottom: 0;
    margin-right: 10px;
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 0.8rem;
    min-width: 120px; /* Ensure consistent label width */
    max-width: 120px;
  }

  /* Enhanced input styling for professional count fields */
  .professional-count-item input {
    width: 70px; /* Increased from 60px */
    min-width: 70px;
    max-width: 70px;
    text-align: center;
    height: 32px; /* Slightly increased height */
    padding: 2px 5px;
    border: 1px solid #c0d6e4; /* Nicer border color */
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    background-color: #f8f9fa;
  }

  .professional-count-item input:focus {
    border-color: #4ca1af;
    box-shadow: 0 0 0 3px rgba(76, 161, 175, 0.15);
    background-color: #fff;
    outline: none;
  }

  .professional-count-item input:hover {
    border-color: #80bdff;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .professional-count-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 576px) {
    .professional-count-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Inline field group for the three fields */
  .three-field-inline {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 1rem;
    gap: 30px; /* Space between each field */
  }

  /* Consistent font size for all labels in the three-field section */
  .three-field-inline label {
    font-size: 0.85rem; /* Set consistent font size for all labels */
    margin-bottom: 0;
    white-space: nowrap;
  }

  /* Style for the professional license field */
  .professional-license-field {
    display: flex;
    align-items: center;
  }

  /* Style for the electric grade field */
  .electric-grade-field {
    display: flex;
    align-items: center;
  }

  /* Style for the human resource field */
  .human-resource-field {
    display: flex;
    align-items: center;
  }

  /* Consistent checkbox styling */
  .three-field-inline .form-check {
    padding-left: 0;
    display: flex;
    align-items: center;
  }

  .three-field-inline .form-check-input {
    margin-right: 8px;
    margin-left: 0;
  }

  /* Consistent select styling */
  .three-field-inline select {
    margin-left: 8px;
    font-size: 0.85rem; /* Match font size with labels */
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .three-field-inline {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  }

  /* Inline field styling for contractor type and name */
  .contractor-info-inline {
    display: flex;
    flex-wrap: wrap;
    margin-left: 20px;
    margin-bottom: 15px;
    Column-gap: 30px;
    row-gap:0px;
  }

  .contractor-field-inline {
    display: flex;
    align-items: center;
  }

  .contractor-field-inline label {
    margin-bottom: 0;
    margin-right: 10px;
    white-space: nowrap;
    font-size: 0.85rem;
    min-width: 120px;
  }

  .contractor-field-inline .form-control {
    width: 200px;
    min-width: 200px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .contractor-info-inline {
      flex-direction: column;
      gap: 15px;
    } 
    .card-body {
  background: linear-gradient(135deg, #e6f5e6, #fff4e1);
  padding: 1.8rem;
  border-radius: 0 0 10px 10px;
  box-shadow: inset 0 0 10px rgba(242, 152, 68, 0.15); /* soft orange inner glow */
  border: 1px solid #f5a62366; /* subtle orange border with transparency */
}
.card {
  background-color: #fff9f0; /* warm creamy beige, soft but not white */
  border: 1px solid #f5a62333; /* light transparent orange border */
  box-shadow: 0 4px 12px rgba(245, 166, 35, 0.15); /* subtle orange shadow around card */
  transition: box-shadow 0.3s ease;
}


.card:hover {
  box-shadow: 0 8px 20px rgba(245, 166, 35, 0.25);
}
.table-hover tbody tr:hover {
  background-color: rgba(246, 139, 30, 0.15);
  transition: background-color 0.3s ease;
}

  }
</style>
<div class="container py-0">
  <div class="row justify-content-center">
    <div class="col-lg-9">
      <div class="form-card">
        <h5 class="form-header text-center mb-0 py-1">የተቋራጭ ምዝገባ ቸክሊስት</h5>

        <div class="form-body">
          {% if success_message %}
          <div class="success-message">
            <i class="bi bi-check-circle-fill"></i>
            {{ success_message }}
          </div>
          {% endif %}

          <form method="post" class="needs-validation" novalidate style="background:";>
            {% csrf_token %}

            <!-- Contractor Info Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">
                <i class="bi bi-building me-1"></i> የተቋራጭ መረጃ
              </h6>
              <div class="fieldset-body">
                <div class="contractor-info-inline">
                  <!-- Contractor type field - inline -->
                  <div class="contractor-field-inline">
                    <label for="{{ form.contractor_type.id_for_label }}">
                      {{ form.contractor_type.label }}
                    </label>
                    {{ form.contractor_type }}
                  </div>
                  <div class="contractor-field-inline">
                    <label for="{{ form.contractor_name.id_for_label }}">
                      {{ form.contractor_name.label }}
                    </label>
                    {{ form.contractor_name }}
                  </div>

                  <!-- Region selection field -->
                  <div class="form-group form-group-col-6">
                    <label for="{{ form.region.id_for_label }}">
                      {{ form.region.label }}
                    </label>
                    {{ form.region }}
                  </div>
                   {% if form.identification_number.value %}
                  <div class="form-group form-group-col-6">
                    <label>{{ form.identification_number.label }}</label>
                    <input
                      type="text"
                      class="form-control"
                      value="{{ form.identification_number.value }}"
                      disabled
                    />
                  </div>
                  {% endif %} 
              </div>
            </div>

            <!-- Human Resources Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">
                <i class="bi bi-people-fill me-1"></i> የሰው ኃይል መረጃ
              </h6>
              <div class="fieldset-body">
                <!-- Three fields in a single inline row with consistent font sizes -->
                <div class="three-field-inline">
                  <!-- Professional license field -->
                  <div class="professional-license-field">
                    <div class="form-check">
                      {{ form.has_professional_license }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_professional_license.id_for_label }}"
                      >
                        {{ form.has_professional_license.label }}
                      </label>
                    </div>
                  </div>

                  <!-- Electric grade field -->
                  <div class="electric-grade-field">
                    <label for="{{ form.electric_grade.id_for_label }}">
                      {{ form.electric_grade.label }}
                    </label>
                    {{ form.electric_grade }}
                  </div>
                 <!-- Human resource field -->
                  <div class="human-resource-field">
                    <div class="form-check">
                      {{ form.has_human_resource }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_human_resource.id_for_label }}"
                      >
                        {{ form.has_human_resource.label }}
                      </label>
                    </div>
                  </div>
                </div>
               <!-- Add a visual separator between the fields and professional count section -->           
                <div class="form-row">
                  <div class="form-group form-group-col-12">
                    <h6
                      class="mb-2 text-muted professional-count-heading"
                      style="font-size: 0.85rem"
                    >
                      <i class="bi bi-person-badge me-1"></i> የባለሙያዎች ብዛት
                    </h6>
                    <!-- Enhanced grid layout for professional counts -->
                    <div class="professional-count-grid">
                      <!-- Column 1 -->
                      <div class="professional-count-item">
                        <label
                          for="{{ form.electrical_engineers.id_for_label }}"
                        >
                          {{ form.electrical_engineers.label }}
                        </label>
                        {{ form.electrical_engineers }}
                      </div>
                      <div class="professional-count-item">
                        <label
                          for="{{ form.electromechanical_engineers.id_for_label }}"
                        >
                          {{ form.electromechanical_engineers.label }}
                        </label>
                        {{ form.electromechanical_engineers }}
                      </div>
                      <!-- Column 2 -->
                      <div class="professional-count-item">
                        <label for="{{ form.civil_engineers.id_for_label }}">
                          {{ form.civil_engineers.label }}
                        </label>
                        {{ form.civil_engineers }}
                      </div>
                      <div class="professional-count-item">
                        <label
                          for="{{ form.electricians_foremen.id_for_label }}"
                        >
                          {{ form.electricians_foremen.label }}
                        </label>
                        {{ form.electricians_foremen }}
                      </div>
                      <!-- Column 3 -->
                      <div class="professional-count-item">
                        <label for="{{ form.surveyors.id_for_label }}">
                          {{ form.surveyors.label }}
                        </label>
                        {{ form.surveyors }}
                      </div>
                      <div class="professional-count-item">
                        <label for="{{ form.line_workers.id_for_label }}">
                          {{ form.line_workers.label }}
                        </label>
                        {{ form.line_workers }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Document Checklist Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">
                <i class="bi bi-file-earmark-check me-1"></i> የሰነድ ቸክሊስት
              </h6>
              <div class="fieldset-body document-checklist">
                <div class="form-row">
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_work_experience_doc }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_work_experience_doc.id_for_label }}"
                      >
                        {{ form.has_work_experience_doc.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_trade_license }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_trade_license.id_for_label }}"
                      >
                        {{ form.has_trade_license.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_energy_certification }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_energy_certification.id_for_label }}"
                      >
                        {{ form.has_energy_certification.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_equipment_resources }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_equipment_resources.id_for_label }}"
                      >
                        {{ form.has_equipment_resources.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_vat_and_tax }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_vat_and_tax.id_for_label }}"
                      >
                        {{ form.has_vat_and_tax.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_good_performance }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_good_performance.id_for_label }}"
                      >
                        {{ form.has_good_performance.label }}
                      </label>
                    </div>
                  </div>
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_financial_capacity }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_financial_capacity.id_for_label }}"
                      >
                        {{ form.has_financial_capacity.label }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Registration Details Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">
                <i class="bi bi-clipboard-check me-1"></i> የምዝገባ ዝርዝሮች
              </h6>
              <div class="fieldset-body">
                <div class="registration-row">
                  <!-- Registration checkbox -->
                  <div class="registration-check">
                    {{ form.is_registered }}
                    <label
                      class="form-check-label"
                      for="{{ form.is_registered.id_for_label }}"
                    >
                      {{ form.is_registered.label }}
                    </label>
                  </div>

                  <!-- Registrar name field - label first -->
                  <div class="registrar-field">
                    <label for="{{ form.registrar_name.id_for_label }}">
                      {{ form.registrar_name.label }}
                    </label>
                    {{ form.registrar_name }}
                  </div>
                  <!-- Registrar ID field - label first -->
                  <div class="registrar-field">
                    <label for="{{ form.registrar_id.id_for_label }}">
                      {{ form.registrar_id.label }}
                    </label>
                    {{ form.registrar_id }}
                  </div>
                </div>
              </div>
            </div>
           <!-- Submit Row -->
            <div class="submit-row">
              <button type="submit" class="btn btn-submit">
                <i class="bi bi-check-circle me-1"></i>አስገባ
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const forms = document.querySelectorAll(".needs-validation");

    Array.from(forms).forEach((form) => {
      form.addEventListener(
        "submit",
        (event) => {
          if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
          }

          form.classList.add("was-validated");
        },
        false
      );
    });
  });
</script>
{% endblock %}
