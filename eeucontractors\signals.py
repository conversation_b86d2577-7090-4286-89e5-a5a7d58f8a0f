from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
import logging
from .models import MainActivity, SubActivity, SubActivityProgress, SubActivityWorkload
from .defaults import DEFAULT_SUBACTIVITIES

logger = logging.getLogger(__name__)

@receiver(post_save, sender=MainActivity)
def create_default_subactivities(sender, instance, created, **kwargs):
    """Create default sub-activities when a new MainActivity is created."""
    if created:
        try:
            main_activity_code = instance.main_activity
            default_subs = DEFAULT_SUBACTIVITIES.get(main_activity_code, [])

            if not default_subs:
                logger.warning(f"No default sub-activities found for main activity: {main_activity_code}")
                return

            # Use bulk_create for better performance
            sub_activities = []
            for sub in default_subs:
                sub_activities.append(SubActivity(
                    main_activity=instance,
                    name=sub['name'],
                    order=sub['order'],
                    weight=sub['weight'],
                    base_duration=sub['base_duration']
                ))

            SubActivity.objects.bulk_create(sub_activities)
            logger.info(f"Created {len(sub_activities)} default sub-activities for {instance}")

        except Exception as e:
            logger.error(f"Error creating default sub-activities for {instance}: {str(e)}")
            # Don't re-raise to avoid breaking the MainActivity creation

@receiver(post_save, sender=SubActivityProgress)
def update_project_progress(sender, instance, created, **kwargs):
    """Update project progress when a sub-activity progress is saved."""
    workload = instance.workload
    project_progress = workload.project_progress
    project_progress.calculate_progress()

@receiver(post_delete, sender=SubActivityProgress)
def update_project_progress_on_delete(sender, instance, **kwargs):
    """Update project progress when a sub-activity progress is deleted."""
    try:
        workload = instance.workload
        if workload and workload.project_progress:
            workload.project_progress.calculate_progress()
    except SubActivityWorkload.DoesNotExist:
        # Workload might have been deleted
        pass

@receiver(post_save, sender=SubActivityWorkload)
def update_project_progress_on_workload_change(sender, instance, **kwargs):
    """Update project progress when a workload is added or modified."""
    if instance.project_progress:
        instance.project_progress.calculate_progress()
