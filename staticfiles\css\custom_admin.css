/* static/css/custom_admin.css */

/* Header banner */
.custom-admin-banner {
    background: linear-gradient(to right, orange, green);
    color: white;
    font-size: 20px;
    font-weight: bold;
    padding: 10px;
    text-align: center;
    border-radius: 6px;
    margin-bottom: 15px;
    font-family: 'Times New Roman', serif;
    letter-spacing: 1px;
}

/* Button styling */
button,
.button,
input[type=submit] {
    background-color: orange !important;
    color: white !important;
    border: 1px solid green !important;
    border-radius: 4px;
}

/* Filter box */
.selector-chosen,
.related-widget-wrapper select {
    border: 1px solid orange !important;
    border-radius: 4px;
}

/* Table header */
#changelist table thead th {
    background-color: green;
    color: white;
}

/* Table hover effect */
#changelist table tbody tr:hover {
    background-color: #fff8e1;
}

/* Form field focus */
input:focus,
select:focus,
textarea:focus {
    border-color: green !important;
    box-shadow: 0 0 5px orange !important;
}