{% comment %} {% extends 'base.html' %}
{% block content %}
<style>
  .top-right-form {
    position: absolute;
    top: 80px;  /* adjust based on navbar height */
    right: 30px;
    width: 320px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1050;
  }

  .top-right-form label {
    font-size: 0.9rem;
    margin-bottom: 4px;
  }

  .top-right-form input {
    font-size: 0.9rem;
    margin-bottom: 10px;
    padding: 6px;
    width: 100%;
    box-sizing: border-box;
  }

  .top-right-form button {
    width: 100%;
    font-size: 0.9rem;
    padding: 6px;
  }

  .top-right-form .message {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }
</style>

<div class="top-right-form">
  <h6 class="mb-3">Change Password</h6>
  {% if messages %}
  {% for message in messages %}
    <div class="message {% if message.tags == 'error' %}text-danger{% else %}text-success{% endif %}">
      {{ message }}
    </div>
  {% endfor %}
{% endif %}

  <form method="POST">
    {% csrf_token %}
    
    <label for="current_password">Current Password</label>
    <input type="password" name="current_password" id="current_password" required>

    <label for="new_password">New Password</label>
    <input type="password" name="new_password" id="new_password" required>

    <label for="confirm_password">Confirm New Password</label>
    <input type="password" name="confirm_password" id="confirm_password" required>

    <button type="submit" class="btn btn-primary btn-sm">Change Password</button>
  </form>
</div>
{% endblock %} {% endcomment %}
