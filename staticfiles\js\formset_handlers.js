document.addEventListener('DOMContentLoaded', function () {
  const addButton = document.querySelector('.add-form-row');
  const formsetContainer = document.querySelector('.formset-container tbody');
  const totalFormsInput = document.querySelector('#id_form-TOTAL_FORMS');

  if (!addButton || !formsetContainer || !totalFormsInput) return;

  function updateAttributes(element, index) {
    if (!element || !element.hasAttribute) return;

    const attrs = ['id', 'name', 'for'];
    attrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        element.setAttribute(attr,
          element.getAttribute(attr).replace(/form-(\d+)-/, `form-${index}-`)
        );
      }
    });

    if (element.children) {
      Array.from(element.children).forEach(child => updateAttributes(child, index));
    }
  }

  function reindexForms() {
    const rows = formsetContainer.querySelectorAll('.formset-row');
    rows.forEach((row, index) => {
      row.querySelectorAll('input, select, textarea, label').forEach(el => updateAttributes(el, index));
    });
    totalFormsInput.value = rows.length;
  }

  function addFormRow() {
    const formRows = formsetContainer.querySelectorAll('.formset-row');
    const lastRow = formRows[formRows.length - 1];
    const newRow = lastRow.cloneNode(true);

    newRow.classList.remove('existing-record');
    newRow.classList.add('new-record');

    newRow.querySelectorAll('input, select, textarea').forEach(input => {
      if (input.type === 'hidden' && input.name.includes('id')) {
        input.value = '';
      } else {
        input.value = '';
      }
    });

    newRow.querySelectorAll('.invalid-feedback').forEach(e => e.remove());

    formsetContainer.appendChild(newRow);
    reindexForms();
  }

  function removeFormRow(el) {
    const row = el.closest('.formset-row');
    if (!row) return;

    const deleteInput = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
    if (deleteInput) {
      deleteInput.checked = true;
      row.style.display = 'none';
    } else {
      row.remove();
      reindexForms();
    }
  }

  addButton.addEventListener('click', function (e) {
    e.preventDefault();
    addFormRow();
  });

  formsetContainer.addEventListener('click', function (e) {
    if (e.target.classList.contains('delete-form-row') || e.target.closest('.delete-form-row')) {
      e.preventDefault();
      removeFormRow(e.target);
    }
  });
});




// document.addEventListener('DOMContentLoaded', function() {
//     // Initialize formset functionality
//     const addButton = document.querySelector('.add-form-row');
//     if (addButton) {
//         addButton.addEventListener('click', function(e) {
//             e.preventDefault();
            
//             const formsetContainer = document.querySelector('.formset-container');
//             const totalForms = document.querySelector('#id_subactivityprogress_set-TOTAL_FORMS');
//             const formNum = parseInt(totalForms.value);
            
//             // Clone the first form
//             const formTemplate = document.querySelector('.formset-row:first-child').cloneNode(true);
            
//             // Update form index
//             formTemplate.innerHTML = formTemplate.innerHTML.replace(/-0-/g, `-${formNum}-`);
//             formTemplate.innerHTML = formTemplate.innerHTML.replace(/_0_/g, `_${formNum}_`);
            
//             // Clear input values
//             formTemplate.querySelectorAll('input:not([type=hidden]):not([type=checkbox])').forEach(input => {
//                 input.value = '';
//             });
            
//             // Reset checkboxes
//             formTemplate.querySelectorAll('input[type=checkbox]').forEach(checkbox => {
//                 checkbox.checked = false;
//             });
            
//             // Add the new form to the container
//             formsetContainer.appendChild(formTemplate);
            
//             // Update total forms count
//             totalForms.value = formNum + 1;
            
//             // Trigger Django's formset:added event
//             const event = new CustomEvent('formset:added', {
//                 detail: { formsetName: 'subactivityprogress_set' },
//                 bubbles: true
//             });
//             formTemplate.dispatchEvent(event);
//         });
//     }
    
//     // Handle delete buttons
//     document.addEventListener('click', function(e) {
//         if (e.target && e.target.classList.contains('delete-form-row')) {
//             e.preventDefault();
            
//             const formRow = e.target.closest('.formset-row');
//             const deleteCheckbox = formRow.querySelector('input[name$="-DELETE"]');
            
//             if (deleteCheckbox) {
//                 // Mark for deletion
//                 deleteCheckbox.checked = true;
//                 formRow.style.display = 'none';
//             } else {
//                 // For new forms, just remove from DOM
//                 formRow.remove();
                
//                 // Update total forms count
//                 const totalForms = document.querySelector('#id_subactivityprogress_set-TOTAL_FORMS');
//                 totalForms.value = parseInt(totalForms.value) - 1;
//             }
            
//             // Trigger Django's formset:removed event
//             const event = new CustomEvent('formset:removed', {
//                 detail: { formsetName: 'subactivityprogress_set' },
//                 bubbles: true
//             });
//             document.dispatchEvent(event);
//         }
//     });
// });