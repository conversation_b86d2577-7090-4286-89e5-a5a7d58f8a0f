{% extends 'base.html' %}
{% load static %}

{% block title %}{{ project.name }} - Project Details{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{{ project.name }}</h2>
    <div>
      {% if perms.eeucontractors.change_project %}
      <a href="{% url 'project_update' project.pk %}" class="btn btn-outline-primary">
        <i class="bi bi-pencil"></i> Edit Project
      </a>
      {% endif %}
      {% if perms.eeucontractors.change_project and not project.approved %}
      <a href="{% url 'project_approval' project.pk %}" class="btn btn-success">
        <i class="bi bi-check-circle"></i> Approve Project
      </a>
      {% endif %}
      <a href="{% url 'project_list' %}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to List
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Project Details -->
    <div class="col-md-8">
      <div class="card mb