{% extends 'base.html' %}
{% load static %}

{% block title %}Projects List{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Projects</h2>
    {% if perms.eeucontractors.add_project %}
    <a href="{% url 'project_create' %}" class="btn btn-primary">
      <i class="bi bi-plus-circle"></i> Add New Project
    </a>
    {% endif %}
  </div>

  <!-- Filters -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0">Filters</h5>
    </div>
    <div class="card-body">
      <form method="get" class="row g-3">
        <div class="col-md-3">
          <label for="status" class="form-label">Status</label>
          <select name="status" id="status" class="form-select">
            <option value="">All Statuses</option>
            {% for status_value, status_label in status_choices %}
            <option value="{{ status_value }}" {% if selected_status == status_value %}selected{% endif %}>
              {{ status_label }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label for="region" class="form-label">Region</label>
          <select name="region" id="region" class="form-select">
            <option value="">All Regions</option>
            {% for region in regions %}
            <option value="{{ region.name }}" {% if selected_region == region.name %}selected{% endif %}>
              {{ region }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label for="type" class="form-label">Type</label>
          <select name="type" id="type" class="form-select">
            <option value="">All Types</option>
            {% for type_value, type_label in type_choices %}
            <option value="{{ type_value }}" {% if selected_type == type_value %}selected{% endif %}>
              {{ type_label }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label for="search" class="form-label">Search</label>
          <input type="text" name="search" id="search" class="form-control" 
                 placeholder="Project or vendor name" value="{{ search_query }}">
        </div>
        <div class="col-12">
          <button type="submit" class="btn btn-primary">Apply Filters</button>
          <a href="{% url 'project_list' %}" class="btn btn-outline-secondary">Clear Filters</a>
        </div>
      </form>
    </div>
  </div>

  <!-- Projects List -->
  {% if projects %}
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead class="table-light">
        <tr>
          <th>Project Name</th>
          <th>Vendor</th>
          <th>Region</th>
          <th>Type</th>
          <th>Status</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>Approved</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for project in projects %}
        <tr>
          <td><a href="{% url 'project_detail' project.id %}">{{ project.name }}</a></td>
          <td>{{ project.vendor.name }}</td>
          <td>{{ project.region }}</td>
          <td>{{ project.get_type_display }}</td>
          <td>
            <span class="badge {% if project.status == 'active' %}bg-success
                  {% elif project.status == 'suspended' %}bg-warning
                  {% else %}bg-secondary{% endif %}">
              {{ project.get_status_display }}
            </span>
          </td>
          <td>{{ project.start_date }}</td>
          <td>{{ project.end_date }}</td>
          <td>
            {% if project.approved %}
            <span class="badge bg-success">Approved</span>
            {% else %}
            <span class="badge bg-secondary">Pending</span>
            {% endif %}
          </td>
          <td>
            <div class="btn-group">
              <a href="{% url 'project_detail' project.id %}" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-eye"></i>
              </a>
              {% if perms.eeucontractors.change_project %}
              <a href="{% url 'project_update' project.id %}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-pencil"></i>
              </a>
              {% endif %}
              {% if perms.eeucontractors.delete_project %}
              <a href="{% url 'project_delete' project.id %}" class="btn btn-sm btn-outline-danger">
                <i class="bi bi-trash"></i>
              </a>
              {% endif %}
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  {% if is_paginated %}
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      {% if page_obj.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">First</a>
      </li>
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
      </li>
      {% endif %}

      <li class="page-item active">
        <span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
      </li>

      {% if page_obj.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
      </li>
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last</a>
      </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}

  {% else %}
  <div class="alert alert-info">
    <i class="bi bi-info-circle"></i> No projects found matching your criteria.
  </div>
  {% endif %}
</div>
{% endblock %}