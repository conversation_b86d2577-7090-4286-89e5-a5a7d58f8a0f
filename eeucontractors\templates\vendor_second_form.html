{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<style>
  .form-card {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #eee;
    background: #fff;
  }

  .form-header {
    background-color: #2c3e50;
    padding: 12px 20px;
    color: white;
    font-size: 1.2rem;
  }

  .form-body {
    padding: 20px;
  }

  .form-control {
    border-radius: 6px;
    padding: 10px 14px;
    border: 1px solid #ccc;
    font-size: 0.95rem;
  }

  .form-control:focus {
    border-color: #4ca1af;
    box-shadow: 0 0 0 0.2rem rgba(76, 161, 175, 0.2);
  }

  .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
  }

  .btn-register {
    background-color: #28a745;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    color: white;
  }

  .btn-register:hover {
    background-color: #218838;
  }

  .btn-outline-secondary {
    border-radius: 6px;
    padding: 10px 20px;
  }

  .alert {
    border-radius: 6px;
    border: none;
    font-size: 0.9rem;
    padding: 10px 15px;
  }

  .text-danger {
    font-size: 0.85rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="form-card">
        <div class="form-header text-center">
          <h4 class="mb-1">Register New Vendor</h4>
        </div>
        
        <div class="form-body">
          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }}" role="alert">
                {{ message }}
              </div>
            {% endfor %}
          {% endif %}
          
          {% if form.non_field_errors %}
            <div class="alert alert-danger" role="alert">
              <ul class="mb-0 ps-3">
                {% for error in form.non_field_errors %}
                  <li>{{ error }}</li>
                {% endfor %}
              </ul>
            </div>
          {% endif %}

          <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row g-3">
              {% for field in form %}
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="{{ field.id_for_label }}" class="form-label">
                      {{ field.label }}
                    </label>

                    {% if field.field.widget.input_type == 'checkbox' %}
                      <div class="form-check">
                        {{ field }}
                        <label class="form-check-label" for="{{ field.id_for_label }}">
                          {{ field.label }}
                        </label>
                      </div>
                    {% else %}
                      {{ field }}
                    {% endif %}

                    {% if field.errors %}
                      <div class="text-danger mt-1">
                        {% for error in field.errors %}
                          <div>{{ error }}</div>
                        {% endfor %}
                      </div>
                    {% endif %}

                    {% if field.help_text %}
                      <div class="form-text text-muted small">{{ field.help_text }}</div>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>

            <div class="d-flex justify-content-between mt-4">
              <a href="{% url 'vendor_information_list' %}" class="btn btn-outline-secondary">
                Back
              </a>
              <button type="submit" class="btn btn-register">
                Register Vendor
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
    formControls.forEach(element => {
      element.classList.add('form-control');
    });

    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      checkbox.classList.add('form-check-input');
    });

    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
      form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
          event.preventDefault();
          event.stopPropagation();
        }
        form.classList.add('was-validated');
      }, false);
    });
  });
</script>
{% endblock %}
