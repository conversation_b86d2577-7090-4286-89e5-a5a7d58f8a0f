/* Sidebar Styles */
.sidebar {
  min-width: 250px;
  max-width: 250px;
  background: #343a40;
  color: #fff;
  transition: all 0.3s;
  height: 100vh;
  position: fixed;
  z-index: 999;
}

.sidebar .sidebar-header {
  padding: 20px;
  background: #212529;
}

.sidebar ul.components {
  padding: 20px 0;
  border-bottom: 1px solid #47748b;
}

.sidebar ul li a {
  padding: 10px;
  font-size: 1.1em;
  display: block;
  color: #fff;
  text-decoration: none;
}

.sidebar ul li a:hover {
  color: #fff;
  background: #495057;
}

.sidebar ul li a.active {
  background: #007bff;
  color: #fff;
}

.sidebar ul li a i {
  margin-right: 10px;
}

.sidebar ul ul a {
  padding-left: 30px !important;
  background: #2c3136;
}

/* Main content wrapper */
.content {
  width: calc(100% - 250px);
  margin-left: 250px;
  padding: 20px;
  min-height: 100vh;
  transition: all 0.3s;
}

/* Toggle button for mobile */
#sidebarCollapse {
  display: none;
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidebar {
    margin-left: -250px;
  }
  .sidebar.active {
    margin-left: 0;
  }
  .content {
    width: 100%;
    margin-left: 0;
  }
  #sidebarCollapse {
    display: block;
  }
}

