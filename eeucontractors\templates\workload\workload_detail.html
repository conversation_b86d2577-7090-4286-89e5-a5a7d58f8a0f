{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>Workload Details</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <h4>Workload Information</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tr>
                    <th>Project:</th>
                    <td>{{ workload.project_progress.project.name }}</td>
                </tr>
                <tr>
                    <th>Vendor:</th>
                    <td>{{ workload.project_progress.vendor.name }}</td>
                </tr>
                <tr>
                    <th>Main Activity:</th>
                    <td>
                        {% if workload.project_progress.main_activity %}
                            {{ workload.project_progress.main_activity.get_main_activity_display }}
                        {% else %}
                            <span class="text-muted">Not assigned</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Sub Activity:</th>
                    <td>{{ workload.sub_activity.name }}</td>
                </tr>
                <tr>
                    <th>Quantity:</th>
                    <td>{{ workload.quantity }}</td>
                </tr>
                <tr>
                    <th>Expected Duration:</th>
                    <td>{{ workload.expected_duration }} days</td>
                </tr>
                <tr>
                    <th>Current Progress:</th>
                    <td>{{ workload.current_progress }}%</td>
                </tr>
            </table>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4>Progress Records</h4>
        </div>
        <div class="card-body">
            {% if progress_records %}
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Progress %</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in progress_records %}
                        <tr>
                            <td>{{ record.progress }}%</td>
                            <td>{{ record.start_date }}</td>
                            <td>{{ record.end_date }}</td>
                            <td>{{ record.actual_duration }} days</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-muted">No progress records found.</p>
            {% endif %}
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h4>Add Progress Record</h4>
        </div>
        <div class="card-body">
            <!-- Debug info -->
            {% if formset.errors %}
            <div class="alert alert-danger">
                <p>Form errors:</p>
                {{ formset.errors }}
            </div>
            {% endif %}
            
            <form method="post">
                {% csrf_token %}
                {{ formset.management_form }}
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>Progress %</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            {% if formset.can_delete %}
                            <th>Delete</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for form in formset %}
                        <tr>
                            <td>
                                {{ form.id }}
                                {{ form.progress }}
                                {% if form.progress.errors %}
                                <div class="text-danger">{{ form.progress.errors }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                <div class="text-danger">{{ form.start_date.errors }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                <div class="text-danger">{{ form.end_date.errors }}</div>
                                {% endif %}
                            </td>
                            {% if formset.can_delete %}
                            <td>{{ form.DELETE }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Save Progress</button>
                    <a href="{% url 'workload_list' progress_id=workload.project_progress.id %}" class="btn btn-secondary">Back to Workloads</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}




