from django.db import models
from django.conf import settings  # ✅ correct import
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.exceptions import ValidationError
import uuid
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth.models import AbstractUser, BaseUserManager


# 1. Custom User Manager
class CustomUserManager(BaseUserManager):
    def create_user(self, username, password=None, region=None, **extra_fields):
        if not username:
            raise ValueError("Username is required.")
        if not password:
            raise ValueError("Password is required.")
        if not region and not extra_fields.get("is_superuser", False):
            raise ValueError("Normal users must have a region.")

        extra_fields.setdefault("is_active", True)
        user = self.model(username=username, region=region, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, password=None, region=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        return self.create_user(username, password, region, **extra_fields)


def generate_custom_id(contractor_type):
    base_uuid = uuid.uuid4().hex[:8].upper()
    if contractor_type == "contractor":
        return f"ctr-{base_uuid}"
    elif contractor_type == "enterprise":
        return f"etr-{base_uuid}"
    elif contractor_type == "new_enterprise":
        return f"netr-{base_uuid}"
    else:
        return f"UNK-{base_uuid}"
class Vendorsregistration(models.Model):
    CONTRACTOR_TYPE_CHOICES = [
        ("contractor", "Contractor"),
        ("enterprise", "Enterprise"),
        ("new_enterprise", "New Enterprise"),
    ]
    ELECTRIC_LEVEL_CHOICES = [
        ('1', 'Level 1'),
        ('2', 'Level 2'),
        ('3', 'Level 3'),
        ('4', 'Level 4'),
        ('5', 'Level 5'),
        ('6', 'Level 6'),
        ('7', 'Level 7'),
        ('8', 'Level 8'),
    ]
    contractor_type = models.CharField(max_length=20, choices=CONTRACTOR_TYPE_CHOICES, verbose_name="የኮንትራክተሩ አይነት")
    contractor_name = models.CharField(max_length=255, verbose_name="የተመዘገበው ስም")
    region = models.ForeignKey("Region", on_delete=models.CASCADE, verbose_name="ሪጅን", related_name="vendors")
    identification_number = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        verbose_name="የመወዳደሪያ መለያ ቁጥር"
    )
    has_professional_license = models.BooleanField(verbose_name="የሙያ ብቃት አቅርቧል?", default=False)
    electric_grade = models.CharField(max_length=2, choices=ELECTRIC_LEVEL_CHOICES, blank=True, verbose_name="የኤሌክትሪክ ስራ ተቋራጭ ደረጃ")
    has_human_resource = models.BooleanField(verbose_name="የሰው ኃይል አሟልቷል?", default=False)
    electrical_engineers = models.PositiveIntegerField(verbose_name="የኤሌክትሪካል ምህንድስና", default=0)
    electromechanical_engineers = models.PositiveIntegerField(verbose_name="የኤሌክትሮ መካኒካል ምህንድስና", default=0)
    civil_engineers = models.PositiveIntegerField(verbose_name="የሲቪል ምህንድስና", default=0)
    electricians_foremen = models.PositiveIntegerField(verbose_name="ኤሌክትሪሺያን/ፎርማን", default=0)
    surveyors = models.PositiveIntegerField(verbose_name="ሰርቬየር", default=0)
    line_workers = models.PositiveIntegerField(verbose_name="የመስመር ሰራተኞች", default=0)
    has_work_experience_doc = models.BooleanField(verbose_name="የሥራ ልምድ ማስረጃ አቅርቧል?", default=False)
    has_trade_license = models.BooleanField(verbose_name="የንግድ ፈቃድ አለው?", default=False)
    has_energy_certification = models.BooleanField(verbose_name="የኤሌክትሪክ ደረጃ የምስክር ወረቀት አቅርቧል?", default=False)
    has_equipment_resources = models.BooleanField(verbose_name="የሰው ኃይልና መሳሪያ አሟልቷል?", default=False)
    has_vat_and_tax = models.BooleanField(verbose_name="VAT እና የግብር የምስክር ወረቀት አቅርቧል?", default=False)
    has_good_performance = models.BooleanField(verbose_name="የመልካም ስራ አፈጻጸም አቅርቧል?", default=False)
    has_financial_capacity = models.BooleanField(verbose_name="የፋይናንስ አቅም መረጃ አቅርቧል?", default=False)
    is_registered = models.BooleanField(verbose_name="ተመዝግቧል?", default=False)
    registrar_name = models.CharField(max_length=255, verbose_name="የመዝጋቢ ሰራተኛው ስም")
    registrar_id = models.CharField(max_length=50, verbose_name="መለያ ቁጥር")
    registration_date = models.DateTimeField(
        verbose_name="የተመዘገበበት ቀን", 
        null=False,  # Change to False to disallow null values
        blank=False,  # Change to False to make it required
        default=timezone.now  # Add default value to handle existing records
    )
    def save(self, *args, **kwargs):
            if not self.identification_number:
                self.identification_number = generate_custom_id(self.contractor_type)

            if not self.registration_date:
                self.registration_date = timezone.now()        
            super().save(*args, **kwargs)
    @property
    def created_at(self):
        """
        Compatibility method to maintain backward compatibility with templates
        that might still use created_at
        """
        return self.registration_date

    def __str__(self):
        return self.contractor_name
class RegionChoices(models.TextChoices):
    EAST_AA = "EAST_AA", "EAST AA"
    SOUTH_AA = "SOUTH_AA", "SOUTH AA"
    WEST_AA = "WEST_AA", "WEST AA "
    NORTH_AA = "NORTH_AA", "NORTH AA"
    AFAR = "AFAR", "AFAR" 
    BAHIRDAR = "BAHIRDAR", "BAHIRDAR"
    DESSIE = "DESSIE", "DESSIE"
    GONDER = "GONDER", "GONDER"
    DEBRE_BIRHAN = "DEBRE_BIRHAN", "DEBRE BIRHAN"
    DEBRE_MARKOS = "DEBRE_MARKOS", "DEBRE MARKOS "
    WOLEDIYA = "WOLEDIYA", "WOLEDIYA"
    BENISHANGULE_GUMUZ = "BENISH" 
    DIRE_DAWA = "DIRE_DAWA", "DIRE DAWA "
    GAMBELA = "GAMBELA", "GAMBELA"
    HARERE = "HARERE", "HARERE"
    ADAMA = "ADAMA", "ADAMA"
    SHEGER = "SHEGER", "Sheger "
    SHASHEMENE = "SHASHEMENE", "SHASHEMENE"
    CHIRO = "CHIRO", "CHIRO"
    JIMMA = "JIMMA", "JIMMA"
    NEKEMITE = "NEKEMITE", "NEKEMITE"
    AMBO = "AMBO", "AMBO"
    BALE_ROBE = "BALE_ROBE", "BALE ROBE"
    METU = "METU", "METU"
    SOMALI = "SOMALI", "SOMALI"
    ARBAMINICH = "ARBAMINICH", "ARBAMINICH "
    CENTRAL_ETHIOPIA = "CENTRAL_ETHIOPIA", "Central Ethiopia"
    WELAYITA = "WELAYITA", "WELAYITA" 
    MEKELE = "MEKELE", "MEKELE"
    SHIRE = "SHIRE", "SHIRE "
    SIDAMA = "SIDAMA", "SIDAMA"
    SOUTH_WEST = "SOUTH_WEST", "SOUTH WEST"
    South_Ethiopia = "SOUTH_ETHIOPIA", "SOUTH ETHIOPIA"
class RegioncodeChoice(models.TextChoices):
    AA = 'AA', 'AA'
    AB = 'AB', 'AB'
    AC = 'AC', 'AC'
    AD = 'AD', 'AD'
    BA = 'BA', 'BA'
    BB = 'BB', 'BB'
    BC = 'BC', 'BC'
    BD = 'BD', 'BD'
    BE = 'BE', 'BE'
    BF = 'BF', 'BF'
    BG = 'BG', 'BG'
    BH = 'BH', 'BH'
    BI = 'BI', 'BI'
    CA = 'CA', 'CA'
    CB = 'CB', 'CB'
    CC = 'CC', 'CC'
    CD = 'CD', 'CD'
    CE = 'CE', 'CE'
    CF = 'CF', 'CF'
    DB = 'DB', 'DB'
    DC = 'DC', 'DC'
    DE = 'DE', 'DE'
    EA = 'EA', 'EA'
    EB = 'EB', 'EB'
    FA = 'FA', 'FA'
    GA = 'GA', 'GA'
    HA = 'HA', 'HA'
    IA = 'IA', 'IA'
    JA = 'JA', 'JA'
    KA = 'KA', 'KA'
    LA = 'LA', 'LA'
    MA = 'MA', 'MA'
class Region(models.Model):
    name = models.CharField(
        max_length=100,
        choices=RegionChoices.choices,
        unique=True
    )
    region_code = models.CharField(
        max_length=10,
        choices=RegioncodeChoice.choices
    )

    def __str__(self):
        return f"{self.name} ({self.region_code})"
# 3. Custom User Model
class CustomUser(AbstractUser):
    region = models.ForeignKey(Region, null=True, blank=True, on_delete=models.SET_NULL)
    objects = CustomUserManager()
from django.db import models
from django.core.exceptions import ValidationError
from django.apps import apps

class Vendor(models.Model):
    vendor_registration = models.ForeignKey('Vendorsregistration', on_delete=models.CASCADE)
      #automatic from model vendorsregistration
    name = models.CharField(max_length=255, editable=False, null=True)
    level = models.CharField(max_length=100, editable=False)
    type = models.CharField(max_length=100, editable=False)
    identification_number = models.CharField(max_length=100, editable=False)
     #manually inserted
    email = models.EmailField()
    phone_number = models.CharField(max_length=20)
    year = models.PositiveIntegerField()
    experience = models.PositiveIntegerField()
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    )
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='inactive', editable=False)
    def __str__(self):
        return self.name or f"Vendor #{self.pk}"  # ✅ Fix added here
    def clean(self):
        # We don’t query related models in clean anymore to avoid the "unsaved instance" error
        if not self.vendor_registration or not self.vendor_registration.pk:
            raise ValidationError("Vendor registration must be selected and saved first.")
    def update_status(self):
        Project = apps.get_model('eeucontractors', 'Project')
        self.status = 'active' if Project.objects.filter(vendor=self, status='active').exists() else 'inactive'
    def save(self, *args, **kwargs):
        if not self.vendor_registration or not self.vendor_registration.pk:
            raise ValidationError("Cannot save vendor: vendor registration must be saved first.")
        self.name = self.vendor_registration.contractor_name
        self.level = self.vendor_registration.electric_grade
        self.type = self.vendor_registration.contractor_type
        self.identification_number = self.vendor_registration.identification_number
        super().save(*args, **kwargs)  # Save before status update
        self.update_status()
        super().save(update_fields=["status"])     
class Project(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    TYPE_CHOICES = [
        ('contractors', 'Contractors'),
        ('enterprise', 'Enterprise'),
        ('new_enterprise', 'New Enterprise'),
    ]
    name = models.CharField(max_length=100)
    region = models.ForeignKey('Region', on_delete=models.CASCADE)
    vendor = models.ForeignKey('Vendor', on_delete=models.CASCADE, related_name='projects')
    level = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(8)],
        help_text="Select vendor level between 1 and 8"
    )
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    main_activity = models.ForeignKey('MainActivity', on_delete=models.CASCADE)
    start_date = models.DateField()
    estimated_end_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    time_consumption = models.DurationField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    approved = models.BooleanField(default=False)
    approved_by = models.CharField(max_length=100, blank=True, null=True)
    approved_date = models.DateField(blank=True, null=True)
    # Map grouped level keys to actual level sets
    LEVEL_RANGE_MAP = {
        'level_1_2_3': {1, 2, 3},
        'level_4_5_6': {4, 5, 6},
        'level_7_8': {7, 8},
    }
    def clean(self):
            main_activity = getattr(self, 'main_activity', None)
            if not main_activity:
              raise ValidationError({'main_activity': "Main activity must be selected."})
            if not self.vendor.vendor_registration_id:
                raise ValidationError("Vendor registration must be selected and saved before saving the project.")
            # Prevent multiple vendor registrations
            Vendor = apps.get_model('eeucontractors', 'Vendor')
            if self.pk is None and Vendor.objects.filter(
                vendor_registration=self.vendor.vendor_registration
            ).exclude(pk=self.vendor.pk).exists():
                raise ValidationError("A vendor is already registered with this vendor registration.")
            # Prevent duplicate active projects
            Project = apps.get_model('eeucontractors', 'Project')
            if self.pk is None and Project.objects.filter(
                vendor__vendor_registration=self.vendor.vendor_registration,
                status='active'
            ).exists():
                raise ValidationError("This vendor has an active project. Cannot register a new project.")
           # Validate level matches the vendor
            try:
                vendor_level = int(self.vendor.level)
            except ValueError:
                raise ValidationError({'level': f"Vendor level ({self.vendor.level}) must be a number between 1 and 8."})
            if self.main_activity:
                required_level_groups = self.main_activity.get_required_levels()
                allowed_levels = set()
                for group in required_level_groups:
                    allowed_levels.update(self.LEVEL_RANGE_MAP.get(group, set()))

                if self.level not in allowed_levels:
                    activity_name = dict(self.main_activity.TitleChoices.choices).get(
                        self.main_activity.main_activity,
                        str(self.main_activity)
                    )
                    raise ValidationError({
                        'level': (
                            f"Level '{self.level}' is not suitable for the selected activity '{activity_name}'. "
                            f"Allowed levels are: {sorted(allowed_levels)}."
                        )
                    })
    def save(self, *args, **kwargs):
        if self.vendor and self.vendor.level:
            self.level = self.vendor.level     
        if self.end_date and self.start_date:
            self.time_consumption = self.end_date - self.start_date
        super().save(*args, **kwargs)
        self.vendor.update_status()
        self.vendor.save()
    def __str__(self):
        return f"{self.name} - {self.status} - {self.vendor}"  
class MainActivity(models.Model):
    class TitleChoices(models.TextChoices):
        SUBSTATION_WORKS = 'substation', '66 and 45 kV substation electrical and civil works'
        LINE_WORKS = 'lines', 'Underground and Overhead 66, 45, 33, 19 and 15 kV lines construction'
        TRANSFORMER_INSTALLATION = 'transformers', 'Installation of 33,19 and 15KV transformers and switchgear'
        LOW_VOLTAGE_LINES = 'low_voltage', '0.4/0.23 kV line construction and maintenance'
        METER_INSTALLATION = 'meters', 'Installation and connection of single and three-phase meters'
    class LevelChoices(models.TextChoices):
        LEVEL_1_2_3 = 'level_1_2_3', 'Level 1, 2 or 3'
        LEVEL_4_5_6 = 'level_4_5_6', 'Level 4, 5 or 6'
        LEVEL_7_8 = 'level_7_8', 'Level 7 or 8'
    main_activity = models.CharField(max_length=50, choices=TitleChoices.choices)
    levels = models.CharField(max_length=20, choices=LevelChoices.choices)
    def __str__(self):
        return f"{self.main_activity}"
    def get_required_levels(self):
        mapping = {
            self.TitleChoices.SUBSTATION_WORKS: [self.LevelChoices.LEVEL_1_2_3],
            self.TitleChoices.LINE_WORKS: [
                self.LevelChoices.LEVEL_1_2_3,
                self.LevelChoices.LEVEL_4_5_6,
                self.LevelChoices.LEVEL_7_8
            ],
            self.TitleChoices.TRANSFORMER_INSTALLATION: [self.LevelChoices.LEVEL_4_5_6],
            self.TitleChoices.LOW_VOLTAGE_LINES: [self.LevelChoices.LEVEL_4_5_6, self.LevelChoices.LEVEL_7_8],
            self.TitleChoices.METER_INSTALLATION: [self.LevelChoices.LEVEL_7_8],
        }
        return mapping.get(self.main_activity, [])
# --------------------
# Sub Activities
# --------------------
class SubActivity(models.Model):
    main_activity = models.ForeignKey(MainActivity, on_delete=models.CASCADE, related_name='subactivities')
    name = models.CharField(max_length=200)
    order = models.PositiveIntegerField()
    weight = models.DecimalField(max_digits=5, decimal_places=2)
    base_duration = models.DecimalField(max_digits=6, decimal_places=2, help_text="Base duration per unit in days")

    def __str__(self):
        return f"{self.main_activity} > {self.name}"
#Use prefetch_related or select_related in views for deep lookups like:
class ProjectProgress(models.Model):
    STATUS_CHOICES = [
        ('on-going', 'On-going'),
        ('delayed', 'Delayed'),
        ('suspended', 'Suspended'),
        ('completed', 'Completed'),
        ('completed with delay', 'Completed with delay')
    ]
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='progress')
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE)
    main_activity = models.ForeignKey(MainActivity, on_delete=models.CASCADE)
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)
    start_date = models.DateField(default=timezone.now)
    completion_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=30, choices=STATUS_CHOICES)
    remarks = models.TextField(blank=True, null=True)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    class Meta:
        ordering = ['-start_date']
    def total_progress(self):
        """
        Calculate total progress with time efficiency factored in.
        This provides a more accurate view of project health by considering both
        completion percentage and schedule adherence.
        
        Returns:
            float: Adjusted progress percentage (0-100)
        """
        total_weighted = 0
        total_weight = 0
        
        for workload in self.workloads.all():
            for progress_record in workload.progress_records.all():
                weight = workload.sub_activity.weight
                compliance_factor = progress_record.time_factor
                adjusted_progress = progress_record.progress * compliance_factor
                total_weighted += adjusted_progress * weight
                total_weight += weight
        
        if total_weight == 0:
            return 0
        
        return round(total_weighted / total_weight, 2)
    def calculate_progress(self):
        """
        Calculate the overall progress percentage based on weighted sub-activities.
        
        Returns:
            float: The calculated progress percentage (0-100)
        """
        workloads = self.workloads.all()
        if not workloads.exists():
            self.progress_percentage = 0
            self.save(update_fields=['progress_percentage'])
            return 0
        
        # Use prefetch_related to reduce database queries
        workloads = workloads.select_related('sub_activity').prefetch_related(
            models.Prefetch(
                'progress_records',
                queryset=SubActivityProgress.objects.order_by('-end_date'),
                to_attr='prefetched_progress'
            )
        )
        
        total_weighted_progress = 0
        total_weight = 0
        
        for workload in workloads:
            # Get the latest progress record from prefetched data
            latest_progress = workload.prefetched_progress[0] if workload.prefetched_progress else None
            if not latest_progress:
                continue
            
            # Use weight from sub_activity if available, otherwise default to 1
            weight = float(getattr(workload.sub_activity, 'weight', 1) or 1)
            
            total_weighted_progress += float(latest_progress.progress * weight)
            total_weight += weight
        
        if total_weight == 0:
            self.progress_percentage = 0
        else:
            self.progress_percentage = round(total_weighted_progress / total_weight, 2)
        
        self.save(update_fields=['progress_percentage'])
        
        # Update completion date if progress is 100%
        if self.progress_percentage == 100 and not self.completion_date:
            self.completion_date = timezone.now().date()
            self.status = 'completed'
            self.save(update_fields=['completion_date', 'status'])
        
        return self.progress_percentage
    def get_progress_timeline(self):
        """
        Generate timeline data for progress visualization.
        
        Returns:
            list: Timeline data with dates and progress percentages
        """
        # Get all progress records across all workloads
        all_progress = SubActivityProgress.objects.filter(workload__project_progress=self).select_related('workload__sub_activity').order_by('end_date')
        
        # Group by date and calculate weighted progress
        timeline = {}
        date_weights = {}
        
        for progress in all_progress:
            date_str = progress.end_date.isoformat()
            weight = progress.workload.sub_activity.weight
            
            if date_str not in timeline:
                timeline[date_str] = 0
                date_weights[date_str] = 0
            
            timeline[date_str] += progress.progress * weight
            date_weights[date_str] += weight
        
        # Calculate percentage for each date
        result = []
        cumulative_progress = 0
        
        for date_str, weighted_sum in sorted(timeline.items()):
            if date_weights[date_str] > 0:
                date_progress = weighted_sum / date_weights[date_str]
                # Use the higher of current calculation or previous cumulative
                cumulative_progress = max(cumulative_progress, date_progress)
                
                result.append({
                    'date': date_str,
                    'progress': round(cumulative_progress, 2)
                })
        
        return result
    
    def get_time_performance(self):
        if not self.start_date:
            return {'status': 'not_started'}
          
        today = timezone.now().date()
        
        # Calculate planned duration based on workloads
        planned_duration = 0
        for workload in self.workloads.all():
            if workload.expected_duration:
                planned_duration = max(planned_duration, float(workload.expected_duration))
        
        # Calculate planned end date
        planned_end_date = self.start_date + timezone.timedelta(days=planned_duration) if planned_duration else None
        
        # Calculate actual duration so far
        actual_duration = (self.completion_date or today) - self.start_date
        actual_duration_days = actual_duration.days
        
        # Calculate time efficiency
        if planned_duration and actual_duration_days > 0:
            time_efficiency = min(planned_duration / actual_duration_days, 1.0) if self.progress_percentage == 100 else \
                              (self.progress_percentage / 100) / (actual_duration_days / planned_duration)
        else:
            time_efficiency = 1.0
        
        return {
            'planned_duration': planned_duration,
            'planned_end_date': planned_end_date,
            'actual_duration': actual_duration_days,
            'time_efficiency': round(time_efficiency, 2),
            'status': 'ahead' if time_efficiency > 1.0 else 'on_track' if time_efficiency >= 0.9 else 'behind'
        }
class SubActivityWorkload(models.Model):
    project_progress = models.ForeignKey(ProjectProgress, on_delete=models.CASCADE, related_name='workloads')
    sub_activity = models.ForeignKey(SubActivity, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    expected_duration = models.PositiveIntegerField(default=1, help_text="Expected duration in days")
    
    @property
    def current_progress(self):
        """Get the current progress percentage for this workload"""
        latest = self.progress_records.order_by('-end_date').first()
        return latest.progress if latest else 0
    @property
    def latest_progress(self):
        """Get the latest progress record for this workload"""
        return self.progress_records.order_by('-end_date').first()
    def __str__(self):
        return f"{self.sub_activity.name} ({self.quantity} units)"
class SubActivityProgress(models.Model):
    workload = models.ForeignKey(SubActivityWorkload, on_delete=models.CASCADE, related_name='progress_records')
    progress = models.PositiveSmallIntegerField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    start_date = models.DateField()
    end_date = models.DateField()
    @property
    def actual_duration(self):
        """Calculate the actual duration in days between start and end date"""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0
    @property
    def time_factor(self):
        """
        Calculate time efficiency factor (1.0 = on time, <1.0 = behind schedule)
        This helps track if work is being completed according to schedule
        """
        if not self.workload.expected_duration or self.actual_duration == 0:
            return 1.0
        
        expected = float(self.workload.expected_duration)
        actual = float(self.actual_duration)
        
        # If completed early or on time
        if actual <= expected:
            return 1.0
        
        # If late, calculate efficiency factor (minimum 0.7)
        factor = expected / actual
        return max(factor, 0.7)
    
    def __str__(self):
        return f"{self.workload.sub_activity.name}: {self.progress}%"
class ProjectVendorsHistory(models.Model):
    STATUS_CHOICES = [
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('suspended', 'Suspended'),
        ('blacklisted', 'Blacklisted'),
    ]
    DELAY_REASON_CHOICES = [
        ('vendor', 'Vendor'),
        ('organization', 'Organization'),
        ('both', 'Both'),
        ('unknown', 'Unknown'),
    ]
    vendor = models.ForeignKey('Vendor', on_delete=models.CASCADE)
    project = models.ForeignKey('Project', on_delete=models.SET_NULL, null=True) 
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='completed'
    )
    evaluation_notes = models.TextField(blank=True, null=True)
    start_date = models.DateField()
    end_date = models.DateField()
    cause_of_delay_or_suspension = models.CharField(
        max_length=20,
        choices=DELAY_REASON_CHOICES,
        blank=True,
        null=True,
        help_text="Specify who caused the delay or suspension if applicable"
    )
    def clean(self):
        if self.status in ['delayed', 'suspended'] and not self.cause_of_delay_or_suspension:
            raise ValidationError("You must specify the cause of delay or suspension.")
        if self.status == 'completed':
            self.cause_of_delay_or_suspension = None
    def __str__(self):
        return f"{self.project} - {self.status}"

class VendorDocument(models.Model):
    DOCUMENT_TYPES = (
        ('license', 'License'),
        ('certificate', 'Certificate'),
        ('id_proof', 'ID Proof'),
        ('other', 'Other'),
    )
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPES)
    file = models.FileField(upload_to='vendor_documents/')
    description = models.TextField(max_length=100,blank=True)
    expiry_date=models.DateField()
    uploaded_at = models.DateTimeField(auto_now_add=True)
    def __str__(self):
          return f"{self.vendor.name} - {self.get_document_type_display()}"

