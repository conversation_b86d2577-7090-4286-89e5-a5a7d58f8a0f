{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
      <h2 class="mb-0">Vendor Performance Report</h2>
    </div>
    <div class="card-body">
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="card bg-light">
            <div class="card-body text-center">
              <h3 class="display-4">{{ vendors.count }}</h3>
              <p class="lead">Total Vendors</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-success text-white">
            <div class="card-body text-center">
              <h3 class="display-4">{{ vendors|dictsortby:"completed_projects"|last.completed_projects|default:"0" }}</h3>
              <p class="lead">Most Completed Projects</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-info text-white">
            <div class="card-body text-center">
              <h3 class="display-4">{{ vendors|dictsortby:"avg_time_factor"|first.avg_time_factor|floatformat:2|default:"0" }}</h3>
              <p class="lead">Best Time Factor</p>
            </div>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th>Vendor Name</th>
              <th>Level</th>
              <th>Completed Projects</th>
              <th>Ongoing Projects</th>
              <th>Time Factor</th>
              <th>Performance Rating</th>
            </tr>
          </thead>
          <tbody>
            {% for vendor in vendors %}
            <tr>
              <td>
                <a href="{% url 'vendor_detail' vendor_id=vendor.id %}">{{ vendor.name }}</a>
              </td>
              <td>{{ vendor.level }}</td>
              <td>{{ vendor.completed_projects }}</td>
              <td>{{ vendor.ongoing_projects }}</td>
              <td>
                {% if vendor.avg_time_factor %}
                  {% if vendor.avg_time_factor <= 0.9 %}
                    <span class="text-success">{{ vendor.avg_time_factor|floatformat:2 }}</span>
                  {% elif vendor.avg_time_factor <= 1.1 %}
                    <span class="text-warning">{{ vendor.avg_time_factor|floatformat:2 }}</span>
                  {% else %}
                    <span class="text-danger">{{ vendor.avg_time_factor|floatformat:2 }}</span>
                  {% endif %}
                {% else %}
                  <span class="text-muted">N/A</span>
                {% endif %}
              </td>
              <td>
                {% if vendor.completed_projects > 0 %}
                  {% if vendor.avg_time_factor <= 0.9 %}
                    <span class="badge bg-success">Excellent</span>
                  {% elif vendor.avg_time_factor <= 1.1 %}
                    <span class="badge bg-warning">Good</span>
                  {% else %}
                    <span class="badge bg-danger">Needs Improvement</span>
                  {% endif %}
                {% else %}
                  <span class="badge bg-secondary">Not Rated</span>
                {% endif %}
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="6" class="text-center">No vendor data available</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock %}