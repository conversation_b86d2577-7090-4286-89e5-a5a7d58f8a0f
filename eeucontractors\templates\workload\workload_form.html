{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow">
    <div class="card-header bg-primary text-white">
      <h3>{{ title }}</h3>
    </div>
    <div class="card-body">
      {% if messages %}
        <div class="messages mb-4">
          {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
          {% endfor %}
        </div>
      {% endif %}
      
      <form method="post">
        {% csrf_token %}
        
        {% for field in form %}
          <div class="mb-3">
            {% if field.is_hidden %}
              {{ field }}
            {% else %}
              <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
              {{ field }}
              {% if field.help_text %}
                <small class="form-text text-muted">{{ field.help_text }}</small>
              {% endif %}
              {% if field.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in field.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            {% endif %}
          </div>
        {% endfor %}
        
        <div class="mt-4">
          <button type="submit" class="btn btn-primary">Save</button>
          {% if workload %}
            <a href="{% url 'workload_detail' workload_id=workload.id %}" class="btn btn-secondary">Cancel</a>
          {% else %}
            <a href="{% url 'workload_list' progress_id=project_progress.id %}" class="btn btn-secondary">Cancel</a>
          {% endif %}
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}